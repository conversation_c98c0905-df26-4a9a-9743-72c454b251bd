<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-section h3 {
            color: #333;
            margin-bottom: 15px;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>登录页面问题诊断工具</h1>
        
        <div class="test-section">
            <h3>1. Particles.js 库加载测试</h3>
            <div id="particlesStatus" class="status warning">正在检测...</div>
            <button onclick="testParticlesJS()">重新检测</button>
        </div>

        <div class="test-section">
            <h3>2. 页面跳转测试</h3>
            <div id="navigationStatus" class="status warning">准备测试</div>
            <button onclick="testNavigation()">测试跳转到data.html</button>
            <button onclick="testNavigationFallback()">测试备用跳转方法</button>
        </div>

        <div class="test-section">
            <h3>3. 网络连接测试</h3>
            <div id="networkStatus" class="status warning">准备测试</div>
            <button onclick="testNetworkConnections()">测试CDN连接</button>
        </div>

        <div class="test-section">
            <h3>4. 控制台日志</h3>
            <div id="consoleLog" class="log">等待日志...</div>
            <button onclick="clearLog()">清空日志</button>
        </div>
    </div>

    <script>
        // 日志记录函数
        function log(message, type = 'info') {
            const logElement = document.getElementById('consoleLog');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            logElement.textContent += logEntry;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(logEntry);
        }

        function clearLog() {
            document.getElementById('consoleLog').textContent = '';
        }

        // 测试 Particles.js 库
        function testParticlesJS() {
            const statusElement = document.getElementById('particlesStatus');
            
            if (typeof particlesJS !== 'undefined') {
                statusElement.className = 'status success';
                statusElement.textContent = '✓ Particles.js 库已成功加载';
                log('Particles.js 库检测成功', 'success');
            } else {
                statusElement.className = 'status error';
                statusElement.textContent = '✗ Particles.js 库未加载 - 将使用CSS备用方案';
                log('Particles.js 库未加载，启用备用方案', 'warning');
                
                // 启用备用方案
                document.body.classList.add('particles-fallback');
            }
        }

        // 测试页面跳转
        function testNavigation() {
            const statusElement = document.getElementById('navigationStatus');
            log('开始测试页面跳转...', 'info');
            
            // 检查data.html是否存在
            fetch('data.html', { method: 'HEAD' })
                .then(response => {
                    if (response.ok) {
                        statusElement.className = 'status success';
                        statusElement.textContent = '✓ data.html 页面存在，可以正常跳转';
                        log('data.html 页面检测成功', 'success');
                    } else {
                        statusElement.className = 'status error';
                        statusElement.textContent = '✗ data.html 页面不存在或无法访问';
                        log('data.html 页面检测失败', 'error');
                    }
                })
                .catch(error => {
                    statusElement.className = 'status error';
                    statusElement.textContent = '✗ 无法检测 data.html 页面: ' + error.message;
                    log('页面检测出错: ' + error.message, 'error');
                });
        }

        // 测试备用跳转方法
        function testNavigationFallback() {
            log('测试备用跳转方法...', 'info');
            
            // 模拟登录成功后的跳转逻辑
            setTimeout(() => {
                try {
                    log('尝试使用 window.location.href 跳转', 'info');
                    // 这里不实际跳转，只是测试
                    if (typeof window.location.href === 'string') {
                        log('window.location.href 可用', 'success');
                    }
                } catch (error) {
                    log('window.location.href 失败，尝试 window.location.replace', 'warning');
                }
            }, 100);
        }

        // 测试网络连接
        function testNetworkConnections() {
            const statusElement = document.getElementById('networkStatus');
            const cdnUrls = [
                'https://cdnjs.cloudflare.com/ajax/libs/particles.js/2.0.0/particles.min.js',
                'https://unpkg.com/particles.js@2.0.0/particles.min.js',
                'https://cdn.jsdelivr.net/particles.js/2.0.0/particles.min.js'
            ];

            statusElement.className = 'status warning';
            statusElement.textContent = '正在测试CDN连接...';
            log('开始测试CDN连接...', 'info');

            Promise.allSettled(
                cdnUrls.map(url => 
                    fetch(url, { method: 'HEAD', mode: 'no-cors' })
                        .then(() => ({ url, status: 'success' }))
                        .catch(error => ({ url, status: 'failed', error: error.message }))
                )
            ).then(results => {
                let successCount = 0;
                results.forEach(result => {
                    if (result.status === 'fulfilled') {
                        successCount++;
                        log(`CDN连接成功: ${result.value.url}`, 'success');
                    } else {
                        log(`CDN连接失败: ${result.reason}`, 'error');
                    }
                });

                if (successCount > 0) {
                    statusElement.className = 'status success';
                    statusElement.textContent = `✓ ${successCount}/${cdnUrls.length} CDN连接成功`;
                } else {
                    statusElement.className = 'status error';
                    statusElement.textContent = '✗ 所有CDN连接失败';
                }
            });
        }

        // 页面加载时自动运行测试
        window.addEventListener('load', function() {
            log('页面加载完成，开始自动诊断...', 'info');
            setTimeout(testParticlesJS, 500);
            setTimeout(testNavigation, 1000);
            setTimeout(testNetworkConnections, 1500);
        });

        // 捕获所有错误
        window.addEventListener('error', function(event) {
            log(`JavaScript错误: ${event.error.message} 在 ${event.filename}:${event.lineno}`, 'error');
        });
    </script>
</body>
</html>
