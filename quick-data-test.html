<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Data页面快速诊断</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: 500;
        }
        .status.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .status.warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 8px;
        }
        button:hover { background: #0056b3; }
        .test-frame {
            width: 100%;
            height: 500px;
            border: 2px solid #ddd;
            border-radius: 5px;
            margin: 20px 0;
        }
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Data页面快速诊断工具</h1>
        
        <div id="mainStatus" class="status warning">准备诊断...</div>
        
        <div class="grid">
            <div>
                <h3>控制面板</h3>
                <button onclick="startDiagnosis()">开始诊断</button>
                <button onclick="testChartJS()">测试Chart.js</button>
                <button onclick="loadDataPage()">加载Data页面</button>
                <button onclick="clearLog()">清空日志</button>
                <button onclick="openDataInNewTab()">新标签页打开</button>
            </div>
            <div>
                <h3>当前状态</h3>
                <div id="statusInfo">
                    <div>Chart.js状态: <span id="chartStatus">检测中...</span></div>
                    <div>页面加载状态: <span id="pageStatus">未开始</span></div>
                    <div>错误计数: <span id="errorCount">0</span></div>
                </div>
            </div>
        </div>
        
        <h3>诊断日志</h3>
        <div id="diagLog" class="log">等待诊断...</div>
        
        <h3>页面预览</h3>
        <iframe id="dataFrame" class="test-frame" src="about:blank"></iframe>
    </div>

    <script>
        let logElement = document.getElementById('diagLog');
        let statusElement = document.getElementById('mainStatus');
        let errorCount = 0;

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            logElement.textContent += logEntry;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(logEntry);
        }

        function updateStatus(message, type) {
            statusElement.textContent = message;
            statusElement.className = `status ${type}`;
        }

        function updateInfo(id, text, color = 'black') {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = text;
                element.style.color = color;
            }
        }

        function clearLog() {
            logElement.textContent = '';
            errorCount = 0;
            updateInfo('errorCount', '0');
        }

        function testChartJS() {
            log('测试Chart.js库...', 'info');
            
            if (typeof Chart !== 'undefined') {
                log(`✓ Chart.js已加载，版本: ${Chart.version}`, 'success');
                updateInfo('chartStatus', '已加载 v' + Chart.version, 'green');
                return true;
            } else {
                log('✗ Chart.js未加载', 'error');
                updateInfo('chartStatus', '未加载', 'red');
                
                // 尝试动态加载
                log('尝试动态加载Chart.js...', 'info');
                const script = document.createElement('script');
                script.src = 'https://cdn.jsdelivr.net/npm/chart.js';
                script.onload = function() {
                    log('✓ Chart.js动态加载成功', 'success');
                    updateInfo('chartStatus', '动态加载成功', 'green');
                };
                script.onerror = function() {
                    log('✗ Chart.js动态加载失败', 'error');
                    updateInfo('chartStatus', '加载失败', 'red');
                };
                document.head.appendChild(script);
                return false;
            }
        }

        function loadDataPage() {
            log('开始加载data.html页面...', 'info');
            updateInfo('pageStatus', '加载中...', 'orange');
            
            const iframe = document.getElementById('dataFrame');
            
            const loadTimeout = setTimeout(() => {
                log('页面加载超时 (15秒)', 'error');
                updateInfo('pageStatus', '加载超时', 'red');
                errorCount++;
                updateInfo('errorCount', errorCount.toString());
            }, 15000);
            
            iframe.onload = function() {
                clearTimeout(loadTimeout);
                log('iframe加载完成', 'success');
                updateInfo('pageStatus', '已加载', 'green');
                
                try {
                    const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                    
                    if (iframeDoc) {
                        log('可以访问iframe文档', 'success');
                        
                        // 检查加载器
                        const loader = iframeDoc.getElementById('pageLoader');
                        if (loader) {
                            log('找到页面加载器', 'info');
                            
                            // 监听加载器状态
                            const checkLoader = () => {
                                if (loader.classList.contains('loaded')) {
                                    log('✓ 页面加载完成（加载器已隐藏）', 'success');
                                    updateInfo('pageStatus', '完全加载', 'green');
                                } else {
                                    log('页面仍在加载中...', 'warning');
                                    setTimeout(checkLoader, 1000);
                                }
                            };
                            
                            setTimeout(checkLoader, 2000);
                        }
                        
                        // 检查Chart.js
                        setTimeout(() => {
                            try {
                                const iframeWindow = iframe.contentWindow;
                                if (iframeWindow.Chart) {
                                    log('✓ iframe中Chart.js可用', 'success');
                                } else {
                                    log('✗ iframe中Chart.js不可用', 'error');
                                    errorCount++;
                                    updateInfo('errorCount', errorCount.toString());
                                }
                            } catch (e) {
                                log('检查iframe Chart.js时出错: ' + e.message, 'warning');
                            }
                        }, 3000);
                        
                    } else {
                        log('无法访问iframe文档', 'error');
                        errorCount++;
                        updateInfo('errorCount', errorCount.toString());
                    }
                } catch (error) {
                    log('检查iframe内容时出错: ' + error.message, 'error');
                    errorCount++;
                    updateInfo('errorCount', errorCount.toString());
                }
            };
            
            iframe.onerror = function() {
                clearTimeout(loadTimeout);
                log('iframe加载失败', 'error');
                updateInfo('pageStatus', '加载失败', 'red');
                errorCount++;
                updateInfo('errorCount', errorCount.toString());
            };
            
            iframe.src = 'data.html';
        }

        function startDiagnosis() {
            log('=== 开始完整诊断 ===', 'info');
            updateStatus('正在诊断...', 'warning');
            clearLog();
            
            // 步骤1: 测试Chart.js
            setTimeout(() => {
                testChartJS();
                
                // 步骤2: 加载页面
                setTimeout(() => {
                    loadDataPage();
                    
                    // 步骤3: 等待并总结
                    setTimeout(() => {
                        log('=== 诊断完成 ===', 'info');
                        if (errorCount === 0) {
                            updateStatus('诊断完成 - 无错误', 'success');
                        } else {
                            updateStatus(`诊断完成 - 发现 ${errorCount} 个问题`, 'error');
                        }
                    }, 10000);
                }, 2000);
            }, 1000);
        }

        function openDataInNewTab() {
            log('在新标签页打开data.html', 'info');
            window.open('data.html', '_blank');
        }

        // 页面加载时初始化
        window.addEventListener('load', function() {
            log('诊断工具已加载', 'info');
            testChartJS();
        });

        // 错误捕获
        window.addEventListener('error', function(event) {
            log(`JavaScript错误: ${event.error.message}`, 'error');
            errorCount++;
            updateInfo('errorCount', errorCount.toString());
        });
    </script>
</body>
</html>
