<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据分析平台 - 数据可视化</title>
    <!-- 图标库 -->
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Google字体 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Chart.js库 - 多重CDN备用方案 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/4.4.0/chart.min.js"
            onerror="this.onerror=null; this.src='https://unpkg.com/chart.js@4.4.0/dist/chart.min.js'"></script>

    <!-- Chart.js插件 - 需要在Chart.js加载完成后加载 -->
    <script>
        // 等待Chart.js加载完成后再加载插件
        function loadChartPlugins() {
            if (typeof Chart !== 'undefined') {
                console.log('Chart.js已加载，开始加载插件...');
                const script = document.createElement('script');
                script.src = 'https://cdnjs.cloudflare.com/ajax/libs/chartjs-plugin-zoom/2.0.1/chartjs-plugin-zoom.min.js';
                script.onerror = function() {
                    console.warn('主CDN插件加载失败，尝试备用CDN...');
                    this.onerror = null;
                    this.src = 'https://unpkg.com/chartjs-plugin-zoom@2.0.1/dist/chartjs-plugin-zoom.min.js';
                };
                script.onload = function() {
                    console.log('Chart.js插件加载成功');
                };
                document.head.appendChild(script);
            } else {
                console.log('Chart.js未加载，1秒后重试...');
                setTimeout(loadChartPlugins, 1000);
            }
        }

        // 页面加载后尝试加载插件
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(loadChartPlugins, 500);
        });
    </script>

    <!-- SheetJS库用于处理Excel文件 -->
    <script src="https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js"></script>
    
    <style>
        /* 重置样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html {
            overflow-x: auto;
            overflow-y: auto;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background:
                radial-gradient(circle at 20% 80%, rgba(147, 197, 253, 0.3) 0%, transparent 60%),
                radial-gradient(circle at 80% 20%, rgba(196, 181, 253, 0.25) 0%, transparent 60%),
                radial-gradient(circle at 40% 40%, rgba(167, 243, 208, 0.2) 0%, transparent 60%),
                radial-gradient(circle at 60% 80%, rgba(254, 202, 202, 0.2) 0%, transparent 60%),
                radial-gradient(circle at 90% 60%, rgba(253, 230, 138, 0.15) 0%, transparent 60%),
                linear-gradient(135deg, #f8fafc 0%, #f1f5f9 20%, #e2e8f0 40%, #f0f9ff 60%, #fef3c7 80%, #fce7f3 100%);
            background-size: 100% 100%, 100% 100%, 100% 100%, 100% 100%, 100% 100%, 100% 100%;
            background-attachment: fixed;
            min-height: 100vh;
            color: #1e293b;
            overflow-x: auto;
            overflow-y: auto;
            position: relative;
            animation: backgroundShift 30s ease-in-out infinite;
        }

        @keyframes backgroundShift {
            0%, 100% {
                background-position: 0% 0%, 100% 100%, 50% 50%, 25% 75%, 90% 60%, 0% 0%;
            }
            16% {
                background-position: 10% 20%, 90% 80%, 30% 70%, 45% 55%, 80% 40%, 16% 16%;
            }
            33% {
                background-position: 30% 40%, 70% 60%, 60% 30%, 65% 35%, 70% 80%, 33% 33%;
            }
            50% {
                background-position: 60% 80%, 40% 20%, 80% 60%, 25% 75%, 60% 20%, 50% 50%;
            }
            66% {
                background-position: 80% 60%, 20% 40%, 40% 80%, 75% 25%, 50% 60%, 66% 66%;
            }
            83% {
                background-position: 40% 20%, 60% 80%, 20% 40%, 55% 65%, 40% 80%, 83% 83%;
            }
        }

        /* 增强动态背景元素 */
        .animated-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
            overflow: hidden;
        }

        .animated-shape {
            position: absolute;
            border-radius: 50%;
            animation: float 25s ease-in-out infinite;
        }

        .shape1 {
            width: 400px;
            height: 400px;
            top: 5%;
            left: 5%;
            background: radial-gradient(circle, rgba(147, 197, 253, 0.4) 0%, rgba(191, 219, 254, 0.15) 100%);
            animation-delay: 0s;
        }

        .shape2 {
            width: 300px;
            height: 300px;
            top: 50%;
            right: 5%;
            background: radial-gradient(circle, rgba(196, 181, 253, 0.35) 0%, rgba(221, 214, 254, 0.15) 100%);
            animation-delay: 8s;
        }

        .shape3 {
            width: 250px;
            height: 250px;
            bottom: 10%;
            left: 15%;
            background: radial-gradient(circle, rgba(167, 243, 208, 0.4) 0%, rgba(209, 250, 229, 0.15) 100%);
            animation-delay: 16s;
        }

        .shape4 {
            width: 350px;
            height: 350px;
            top: 25%;
            right: 25%;
            background: radial-gradient(circle, rgba(254, 202, 202, 0.35) 0%, rgba(254, 226, 226, 0.15) 100%);
            animation-delay: 12s;
        }

        .shape5 {
            width: 200px;
            height: 200px;
            top: 70%;
            left: 60%;
            background: radial-gradient(circle, rgba(253, 230, 138, 0.4) 0%, rgba(254, 240, 138, 0.15) 100%);
            animation-delay: 4s;
        }

        .shape6 {
            width: 180px;
            height: 180px;
            top: 15%;
            left: 70%;
            background: radial-gradient(circle, rgba(252, 231, 243, 0.35) 0%, rgba(253, 242, 248, 0.15) 100%);
            animation-delay: 20s;
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px) translateX(0px) rotate(0deg) scale(1);
                opacity: 0.4;
            }
            25% {
                transform: translateY(-30px) translateX(20px) rotate(90deg) scale(1.1);
                opacity: 0.2;
            }
            50% {
                transform: translateY(-60px) translateX(-10px) rotate(180deg) scale(0.9);
                opacity: 0.6;
            }
            75% {
                transform: translateY(-20px) translateX(-30px) rotate(270deg) scale(1.05);
                opacity: 0.3;
            }
        }

        /* 粒子效果 */
        .particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: rgba(255, 255, 255, 0.6);
            border-radius: 50%;
            animation: particleFloat 15s linear infinite;
        }

        @keyframes particleFloat {
            0% {
                transform: translateY(100vh) translateX(0px);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(-100px) translateX(100px);
                opacity: 0;
            }
        }

        /* 光效 */
        .light-effects {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .light-beam {
            position: absolute;
            width: 2px;
            height: 100%;
            background: linear-gradient(to bottom,
                transparent 0%,
                rgba(255, 255, 255, 0.1) 50%,
                transparent 100%);
            animation: lightSweep 12s ease-in-out infinite;
        }

        .light-beam:nth-child(1) { left: 10%; animation-delay: 0s; }
        .light-beam:nth-child(2) { left: 30%; animation-delay: 4s; }
        .light-beam:nth-child(3) { left: 60%; animation-delay: 8s; }
        .light-beam:nth-child(4) { left: 85%; animation-delay: 2s; }

        @keyframes lightSweep {
            0%, 100% {
                opacity: 0;
                transform: scaleY(0);
            }
            50% {
                opacity: 1;
                transform: scaleY(1);
            }
        }

        /* 页面加载动画 */
        .page-loader {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 25%, #bae6fd 50%, #7dd3fc 75%, #38bdf8 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            transition: opacity 0.6s ease, visibility 0.6s ease;
        }

        .page-loader.loaded {
            opacity: 0;
            visibility: hidden;
        }

        .loader-content {
            text-align: center;
            color: #0369a1;
        }

        .spinner {
            position: relative;
            width: 80px;
            height: 80px;
            margin: 0 auto 25px;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .spinner-ring {
            position: absolute;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            border: 4px solid transparent;
            border-top-color: #0369a1;
            animation: spin 1.5s cubic-bezier(0.68, -0.55, 0.27, 1.55) infinite;
        }

        .spinner-ring:nth-child(1) {
            animation-delay: 0s;
        }

        .spinner-ring:nth-child(2) {
            width: 80%;
            height: 80%;
            border-top-color: #0284c7;
            animation-delay: 0.15s;
            animation-direction: reverse;
        }

        .spinner-ring:nth-child(3) {
            width: 60%;
            height: 60%;
            border-top-color: #0ea5e9;
            animation-delay: 0.3s;
        }

        .spinner-dot {
            position: absolute;
            width: 20px;
            height: 20px;
            background: #0369a1;
            border-radius: 50%;
            box-shadow: 0 0 10px rgba(3, 105, 161, 0.5);
            animation: spinnerDot 1.5s ease-in-out infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @keyframes spinnerDot {
            0%, 100% {
                transform: scale(0.8);
                opacity: 0.8;
            }
            50% {
                transform: scale(1.2);
                opacity: 1;
            }
        }

        .loading-text {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
            letter-spacing: 1px;
        }

        .loading-progress {
            width: 200px;
            height: 4px;
            background: rgba(3, 105, 161, 0.2);
            border-radius: 2px;
            overflow: hidden;
            margin: 0 auto;
        }

        .loading-bar {
            height: 100%;
            background: linear-gradient(90deg, #0369a1, #0ea5e9);
            border-radius: 2px;
            animation: loadingProgress 2s ease-in-out infinite;
        }

        @keyframes loadingProgress {
            0% {
                width: 0%;
                transform: translateX(-100%);
            }
            50% {
                width: 100%;
                transform: translateX(0%);
            }
            100% {
                width: 100%;
                transform: translateX(100%);
            }
        }

        /* 主容器布局 */
        .main-container {
            display: grid;
            grid-template-columns: 260px 1fr;
            min-height: 100vh;
            height: auto;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            min-width: 1750px;
        }

        /* 固定水平滚动条 */
        .horizontal-scrollbar {
            position: fixed !important;
            bottom: 0 !important;
            left: 0 !important;
            right: 0 !important;
            height: 25px !important;
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(10px);
            border-top: 2px solid rgba(59, 130, 246, 0.3) !important;
            z-index: 9999 !important;
            overflow-x: auto !important;
            overflow-y: hidden !important;
            box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1) !important;
            display: block !important;
            visibility: visible !important;
            width: 100% !important;
        }

        .horizontal-scrollbar-content {
            height: 20px;
            width: 3000px;
            background: linear-gradient(90deg, rgba(59, 130, 246, 0.1) 0%, rgba(59, 130, 246, 0.05) 100%);
            min-width: 3000px;
        }

        /* 确保滚动条可见 */
        .horizontal-scrollbar::-webkit-scrollbar {
            height: 12px;
        }

        .horizontal-scrollbar::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.1);
            border-radius: 6px;
        }

        .horizontal-scrollbar::-webkit-scrollbar-thumb {
            background: rgba(59, 130, 246, 0.6);
            border-radius: 6px;
        }

        .horizontal-scrollbar::-webkit-scrollbar-thumb:hover {
            background: rgba(59, 130, 246, 0.8);
        }

        /* 左侧导航栏 */
        .sidebar {
            background: rgba(255, 255, 255, 0.92);
            backdrop-filter: blur(25px);
            border-right: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow:
                4px 0 25px rgba(0, 0, 0, 0.06),
                2px 0 15px rgba(59, 130, 246, 0.08);
            overflow-y: auto;
            position: relative;
            padding: 15px 12px;
        }

        .sidebar-header {
            padding: 20px 15px;
            text-align: center;
            border-bottom: 1px solid rgba(59, 130, 246, 0.1);
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(14, 165, 233, 0.1) 100%);
        }

        .logo {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #3b82f6, #0ea5e9);
            border-radius: 16px;
            margin: 0 auto 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
            transition: transform 0.3s ease;
        }

        .logo:hover {
            transform: scale(1.05);
        }

        .sidebar-title {
            font-size: 20px;
            font-weight: 700;
            color: #1e293b;
            margin: 0;
            background: linear-gradient(135deg, #3b82f6, #0ea5e9);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* 导航菜单 */
        .nav-section {
            padding: 25px 20px;
        }

        .nav-category {
            margin-bottom: 25px;
        }

        .category-header {
            display: flex;
            align-items: center;
            padding: 15px 20px;
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(14, 165, 233, 0.1) 100%);
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid rgba(59, 130, 246, 0.2);
        }

        .category-header:hover {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.15) 0%, rgba(14, 165, 233, 0.15) 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.2);
        }

        .category-icon {
            font-size: 18px;
            color: #3b82f6;
            margin-right: 12px;
            width: 20px;
        }

        .category-title {
            flex: 1;
            font-size: 16px;
            font-weight: 600;
            color: #1e293b;
        }

        .category-arrow {
            font-size: 12px;
            color: #3b82f6;
            transition: transform 0.3s ease;
        }

        .category-header.expanded .category-arrow {
            transform: rotate(180deg);
        }

        .nav-submenu {
            list-style: none;
            margin-top: 10px;
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }

        .nav-category.expanded .nav-submenu {
            max-height: 300px;
        }

        .nav-item {
            margin: 5px 0;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 12px 20px 12px 45px;
            color: #64748b;
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.3s ease;
            position: relative;
        }

        .nav-link::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            width: 0;
            height: 100%;
            background: linear-gradient(90deg, rgba(59, 130, 246, 0.1), transparent);
            border-radius: 8px;
            transition: width 0.3s ease;
        }

        .nav-link:hover::before {
            width: 100%;
        }

        .nav-link:hover {
            color: #3b82f6;
            transform: translateX(8px);
        }

        .nav-icon {
            font-size: 16px;
            margin-right: 10px;
            width: 18px;
            color: #94a3b8;
            transition: color 0.3s ease;
        }

        .nav-link:hover .nav-icon {
            color: #3b82f6;
        }

        .nav-link.active {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.15) 0%, rgba(14, 165, 233, 0.15) 100%);
            color: #3b82f6;
            border-left: 4px solid #3b82f6;
            transform: translateX(8px);
            box-shadow: 0 4px 15px rgba(59, 130, 246, 0.2);
        }

        .nav-link.active .nav-icon {
            color: #3b82f6;
        }

        .nav-link.active::before {
            width: 100%;
        }

        .nav-text {
            font-size: 14px;
            font-weight: 500;
        }

        /* 意见反馈样式 */
        .feedback-section {
            position: absolute;
            bottom: 20px;
            left: 12px;
            right: 12px;
            padding: 15px;
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(14, 165, 233, 0.1) 100%);
            border-radius: 12px;
            border: 1px solid rgba(59, 130, 246, 0.2);
        }

        .feedback-link {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            padding: 12px 20px;
            color: #3b82f6;
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.8);
            border: 1px solid rgba(59, 130, 246, 0.2);
        }

        .feedback-link:hover {
            background: linear-gradient(135deg, #3b82f6, #0ea5e9);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
        }

        .feedback-icon {
            font-size: 16px;
            transition: transform 0.3s ease;
        }

        .feedback-link:hover .feedback-icon {
            transform: scale(1.1);
        }

        .feedback-text {
            font-size: 14px;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <!-- 页面加载动画 -->
    <div class="page-loader" id="pageLoader">
        <div class="loader-content">
            <div class="spinner">
                <div class="spinner-ring"></div>
                <div class="spinner-ring"></div>
                <div class="spinner-ring"></div>
                <div class="spinner-dot"></div>
            </div>
            <div class="loading-text">正在加载数据可视化平台...</div>
            <div class="loading-progress">
                <div class="loading-bar"></div>
            </div>
        </div>
    </div>

    <!-- 增强动态背景元素 -->
    <div class="animated-bg">
        <div class="animated-shape shape1"></div>
        <div class="animated-shape shape2"></div>
        <div class="animated-shape shape3"></div>
        <div class="animated-shape shape4"></div>
        <div class="animated-shape shape5"></div>
        <div class="animated-shape shape6"></div>
    </div>

    <!-- 粒子效果 -->
    <div class="particles" id="particles"></div>

    <!-- 光效 -->
    <div class="light-effects">
        <div class="light-beam"></div>
        <div class="light-beam"></div>
        <div class="light-beam"></div>
        <div class="light-beam"></div>
    </div>

    <!-- 主容器 -->
    <div class="main-container">
        <!-- 左侧导航栏 -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <i class="fas fa-chart-line"></i>
                </div>
                <h1 class="sidebar-title">数据分析平台</h1>
            </div>

            <nav class="nav-section">
                <!-- 信号可视化分类 -->
                <div class="nav-category expanded">
                    <div class="category-header expanded" data-category="signal-visualization">
                        <i class="fas fa-chart-line category-icon"></i>
                        <span class="category-title">信号可视化</span>
                        <i class="fas fa-chevron-down category-arrow"></i>
                    </div>
                    <ul class="nav-submenu">
                        <li class="nav-item">
                            <a href="data.html" class="nav-link active" data-page="data">
                                <i class="fas fa-upload nav-icon"></i>
                                <span class="nav-text">数据上传</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="serial-debug.html" class="nav-link" data-page="serial-debug">
                                <i class="fas fa-terminal nav-icon"></i>
                                <span class="nav-text">串口调试</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- 信号处理分类 -->
                <div class="nav-category">
                    <div class="category-header" data-category="signal-processing">
                        <i class="fas fa-wave-square category-icon"></i>
                        <span class="category-title">信号处理</span>
                        <i class="fas fa-chevron-down category-arrow"></i>
                    </div>
                    <ul class="nav-submenu">
                        <li class="nav-item">
                            <a href="signal-denoise.html" class="nav-link" data-page="signal-denoise">
                                <i class="fas fa-magic nav-icon"></i>
                                <span class="nav-text">信号去噪</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- 信号分析分类 -->
                <div class="nav-category expanded">
                    <div class="category-header expanded" data-category="signal-analysis">
                        <i class="fas fa-chart-area category-icon"></i>
                        <span class="category-title">信号分析</span>
                        <i class="fas fa-chevron-down category-arrow"></i>
                    </div>
                    <ul class="nav-submenu">
                        <li class="nav-item">
                            <a href="fft-analysis.html" class="nav-link" data-page="fft-analysis">
                                <i class="fas fa-chart-bar nav-icon"></i>
                                <span class="nav-text">频谱分析</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- 意见反馈 - 底部固定位置 -->
                <div class="feedback-section">
                    <a href="feedback.html" class="feedback-link">
                        <i class="fas fa-comment-dots feedback-icon"></i>
                        <span class="feedback-text">意见反馈</span>
                    </a>
                </div>
            </nav>
        </aside>

        <!-- 主内容区域 -->
        <main class="main-content">
            <style>
                /* 主内容区域样式 */
                .main-content {
                    padding: 15px 20px 30px 20px;
                    background: rgba(255, 255, 255, 0.02);
                    min-height: 100vh;
                    box-sizing: border-box;
                    display: flex;
                    flex-direction: column;
                    overflow-x: auto;
                    overflow-y: auto;
                }

                /* 内容布局 */
                .content-layout {
                    display: grid;
                    grid-template-columns: 320px 1fr;
                    gap: 25px;
                    min-width: 1750px;
                    width: max-content;
                    align-items: stretch;
                    padding: 20px;
                    padding-bottom: 15px;
                    flex: 1;
                    min-height: 0;
                }

                /* 左侧控制面板 */
                .control-panel {
                    display: flex;
                    flex-direction: column;
                    gap: 15px;
                    overflow-y: auto;
                    padding-bottom: 10px;
                    padding-right: 5px;
                    /* 高度将通过JavaScript动态设置 */
                    flex-shrink: 0;
                    min-height: 0;
                }

                .control-card {
                    background: rgba(255, 255, 255, 0.95);
                    backdrop-filter: blur(25px);
                    border-radius: 20px;
                    padding: 20px;
                    box-shadow:
                        0 10px 40px rgba(0, 0, 0, 0.06),
                        0 4px 20px rgba(59, 130, 246, 0.08),
                        inset 0 1px 0 rgba(255, 255, 255, 0.8);
                    border: 1px solid rgba(255, 255, 255, 0.3);
                    transition: all 0.3s ease;
                    position: relative;
                    overflow: visible;
                    flex-shrink: 0;
                    width: 100%;
                    box-sizing: border-box;
                }

                .control-card:first-child {
                    flex: 0 0 auto;
                    margin-bottom: 0;
                }

                .control-card.sample-data-card {
                    flex: 0 0 auto;
                    min-height: auto;
                    height: fit-content;
                }

                /* 统计分析卡片特定样式 */
                #statisticsCard {
                    height: fit-content;
                    min-height: auto;
                    max-height: fit-content;
                    overflow: visible;
                    padding-bottom: 20px !important;
                    background: rgba(255, 255, 255, 0.95) !important;
                    backdrop-filter: blur(25px) !important;
                }

                /* 统计分析内容区域 */
                #statisticsCard .statistics-content {
                    margin-bottom: 0;
                    padding-bottom: 0;
                }

                .control-card::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 1px;
                    background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.3), transparent);
                }

                .control-card:hover {
                    transform: translateY(-10px) scale(1.02);
                    box-shadow:
                        0 25px 70px rgba(0, 0, 0, 0.12),
                        0 8px 30px rgba(59, 130, 246, 0.15),
                        inset 0 1px 0 rgba(255, 255, 255, 0.9);
                    border-color: rgba(255, 255, 255, 0.5);
                }

                .control-card:active {
                    transform: translateY(-4px) scale(0.98);
                    transition: all 0.1s ease;
                }

                .control-card h3 {
                    font-size: 18px;
                    font-weight: 600;
                    color: #1e293b;
                    margin-bottom: 20px;
                    display: flex;
                    align-items: center;
                    gap: 10px;
                }

                .control-card h3 i {
                    color: #3b82f6;
                    font-size: 20px;
                }

                /* 导入数据卡片 */
                .import-options {
                    display: flex;
                    flex-direction: column;
                    gap: 15px;
                }

                .import-option {
                    padding: 20px;
                    border: 2px dashed rgba(59, 130, 246, 0.3);
                    border-radius: 12px;
                    text-align: center;
                    cursor: pointer;
                    transition: all 0.3s ease;
                    background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(14, 165, 233, 0.05) 100%);
                }

                .import-option:hover {
                    border-color: #3b82f6;
                    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(14, 165, 233, 0.1) 100%);
                    transform: translateY(-2px);
                    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.2);
                }

                .import-option i {
                    font-size: 32px;
                    color: #3b82f6;
                    margin-bottom: 12px;
                    display: block;
                }

                .import-option h4 {
                    font-size: 16px;
                    font-weight: 600;
                    color: #1e293b;
                    margin: 0 0 8px 0;
                }

                .import-option p {
                    font-size: 14px;
                    color: #64748b;
                    margin: 0;
                }

                /* 加载示例数据卡片 */
                .sample-data-card {
                    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(14, 165, 233, 0.1) 100%);
                    border: 1px solid rgba(59, 130, 246, 0.2);
                }

                .sample-grid {
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 12px;
                    margin-bottom: 20px;
                }

                .sample-item {
                    padding: 15px;
                    background: rgba(255, 255, 255, 0.8);
                    border-radius: 8px;
                    text-align: center;
                    cursor: pointer;
                    transition: all 0.3s ease;
                    border: 1px solid rgba(59, 130, 246, 0.1);
                }

                .sample-item:hover {
                    background: rgba(59, 130, 246, 0.1);
                    transform: scale(1.02);
                }

                .sample-item.active {
                    background: linear-gradient(135deg, #3b82f6, #0ea5e9);
                    color: white;
                    border-color: #3b82f6;
                }

                .sample-item i {
                    font-size: 20px;
                    margin-bottom: 8px;
                    display: block;
                    color: #3b82f6;
                }

                .sample-item.active i {
                    color: white;
                }

                .sample-item span {
                    font-size: 12px;
                    font-weight: 500;
                }

                .sample-controls {
                    margin-bottom: 15px;
                    padding: 15px;
                    background: rgba(255, 255, 255, 0.8);
                    border-radius: 8px;
                }

                .control-group {
                    margin-bottom: 12px;
                }

                .control-group label {
                    display: block;
                    font-size: 12px;
                    font-weight: 500;
                    color: #64748b;
                    margin-bottom: 5px;
                }

                .control-group input[type="range"] {
                    width: 100%;
                    height: 4px;
                    background: rgba(59, 130, 246, 0.2);
                    border-radius: 2px;
                    outline: none;
                    -webkit-appearance: none;
                    appearance: none;
                }

                .control-group input[type="range"]::-webkit-slider-thumb {
                    -webkit-appearance: none;
                    appearance: none;
                    width: 16px;
                    height: 16px;
                    background: #3b82f6;
                    border-radius: 50%;
                    cursor: pointer;
                }

                .control-group .checkbox-wrapper {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                }

                .control-group input[type="checkbox"] {
                    width: 16px;
                    height: 16px;
                    accent-color: #3b82f6;
                }

                /* 美化的噪声开关样式 */
                .noise-toggle-container {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    padding: 15px 20px;
                    background: linear-gradient(135deg, rgba(59, 130, 246, 0.08) 0%, rgba(14, 165, 233, 0.08) 100%);
                    border: 2px solid rgba(59, 130, 246, 0.2);
                    border-radius: 12px;
                    transition: all 0.3s ease;
                    position: relative;
                    overflow: hidden;
                    max-width: 400px;
                    margin: 0 auto;
                }

                .noise-toggle-container::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: -100%;
                    width: 100%;
                    height: 100%;
                    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
                    transition: left 0.5s;
                }

                .noise-toggle-container:hover::before {
                    left: 100%;
                }

                .noise-toggle-container:hover {
                    background: linear-gradient(135deg, rgba(59, 130, 246, 0.12) 0%, rgba(14, 165, 233, 0.12) 100%);
                    border-color: rgba(59, 130, 246, 0.4);
                    transform: translateY(-2px);
                    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.2);
                }

                .noise-toggle-text-section {
                    display: flex;
                    align-items: center;
                    gap: 10px;
                    font-size: 14px;
                    font-weight: 600;
                    color: #1e293b;
                }

                .noise-toggle-button-section {
                    display: flex;
                    align-items: center;
                }

                .noise-toggle-checkbox {
                    display: none;
                }

                .noise-toggle-label {
                    cursor: pointer;
                    display: inline-block;
                }

                .noise-toggle-switch {
                    position: relative;
                    width: 50px;
                    height: 26px;
                    background: rgba(148, 163, 184, 0.3);
                    border-radius: 13px;
                    transition: all 0.3s ease;
                    border: 2px solid rgba(148, 163, 184, 0.2);
                    cursor: pointer;
                    display: inline-block;
                }

                .noise-toggle-slider {
                    position: absolute;
                    top: 2px;
                    left: 2px;
                    width: 18px;
                    height: 18px;
                    background: linear-gradient(135deg, #94a3b8, #64748b);
                    border-radius: 50%;
                    transition: all 0.3s ease;
                    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
                }

                .noise-toggle-checkbox:checked + .noise-toggle-label .noise-toggle-switch {
                    background: linear-gradient(135deg, rgba(59, 130, 246, 0.8), rgba(14, 165, 233, 0.8));
                    border-color: rgba(59, 130, 246, 0.6);
                }

                .noise-toggle-checkbox:checked + .noise-toggle-label .noise-toggle-slider {
                    transform: translateX(24px);
                    background: linear-gradient(135deg, #3b82f6, #0ea5e9);
                    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.4);
                }

                .noise-toggle-text {
                    display: flex;
                    align-items: center;
                    gap: 10px;
                }

                .noise-toggle-text i {
                    font-size: 16px;
                    color: #3b82f6;
                    transition: all 0.3s ease;
                }

                .noise-toggle-checkbox:checked + .noise-toggle-label .noise-toggle-text i {
                    color: #0ea5e9;
                    transform: scale(1.1);
                }

                .freq-scale {
                    text-align: center;
                    margin-top: 5px;
                }

                .freq-scale small {
                    color: #94a3b8;
                    font-size: 10px;
                }

                /* 参数信息样式 */
                .parameter-info {
                    margin-top: 12px;
                    padding: 8px 12px;
                    background: rgba(59, 130, 246, 0.05);
                    border-radius: 8px;
                    border-left: 3px solid rgba(59, 130, 246, 0.3);
                }

                .parameter-info small {
                    color: #64748b;
                    font-size: 12px;
                    display: flex;
                    align-items: center;
                    gap: 6px;
                }

                .parameter-info i {
                    color: rgba(59, 130, 246, 0.6);
                }

                /* 统计分析卡片样式 */
                #statisticsCard {
                    flex: 0 0 auto;
                    display: flex;
                    flex-direction: column;
                    min-height: 0;
                    background: rgba(255, 255, 255, 0.95) !important;
                    backdrop-filter: blur(25px) !important;
                }

                /* 统计分析内容区域 */
                #statisticsCard .statistics-content {
                    flex: 1;
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 12px;
                    justify-content: flex-start;
                }

                /* 噪声配置面板样式 */
                .noise-config-panel {
                    margin-top: 15px;
                    padding: 15px;
                    background: linear-gradient(135deg, rgba(59, 130, 246, 0.08) 0%, rgba(14, 165, 233, 0.08) 100%);
                    border-radius: 12px;
                    border: 1px solid rgba(59, 130, 246, 0.2);
                    transition: all 0.3s ease;
                }

                .noise-config-panel.show {
                    display: block !important;
                    animation: slideDown 0.3s ease;
                }

                @keyframes slideDown {
                    from {
                        opacity: 0;
                        transform: translateY(-10px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }

                .noise-types-grid {
                    display: grid;
                    grid-template-columns: repeat(2, 1fr);
                    gap: 8px;
                    margin-bottom: 15px;
                }

                .noise-type-item {
                    position: relative;
                }

                .noise-type-item input[type="checkbox"] {
                    display: none;
                }

                .noise-type-item label {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    padding: 10px 12px;
                    background: rgba(255, 255, 255, 0.9);
                    border: 2px solid rgba(59, 130, 246, 0.2);
                    border-radius: 8px;
                    cursor: pointer;
                    transition: all 0.3s ease;
                    font-size: 12px;
                    font-weight: 500;
                    color: #64748b;
                    margin: 0;
                }

                .noise-type-item label:hover {
                    background: rgba(59, 130, 246, 0.1);
                    border-color: rgba(59, 130, 246, 0.4);
                    transform: translateY(-2px);
                    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
                }

                .noise-type-item input[type="checkbox"]:checked + label {
                    background: linear-gradient(135deg, #3b82f6, #0ea5e9);
                    border-color: #3b82f6;
                    color: white;
                    transform: translateY(-2px);
                    box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
                }

                .noise-type-item label i {
                    font-size: 14px;
                    width: 16px;
                    text-align: center;
                }

                .noise-type-item input[type="checkbox"]:checked + label i {
                    color: white;
                }

                .noise-amplitude-control {
                    margin-top: 15px;
                    padding-top: 15px;
                    border-top: 1px solid rgba(59, 130, 246, 0.2);
                }

                .noise-amplitude-control label {
                    display: block;
                    font-size: 12px;
                    font-weight: 500;
                    color: #64748b;
                    margin-bottom: 8px;
                }

                .noise-amplitude-control input[type="range"] {
                    width: 100%;
                    height: 4px;
                    background: rgba(59, 130, 246, 0.2);
                    border-radius: 2px;
                    outline: none;
                    -webkit-appearance: none;
                    appearance: none;
                    margin-bottom: 5px;
                }

                .noise-amplitude-control input[type="range"]::-webkit-slider-thumb {
                    -webkit-appearance: none;
                    appearance: none;
                    width: 18px;
                    height: 18px;
                    background: linear-gradient(135deg, #3b82f6, #0ea5e9);
                    border-radius: 50%;
                    cursor: pointer;
                    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
                    transition: all 0.3s ease;
                }

                .noise-amplitude-control input[type="range"]::-webkit-slider-thumb:hover {
                    transform: scale(1.2);
                    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.5);
                }

                .amplitude-scale {
                    text-align: center;
                    margin-top: 5px;
                }

                .amplitude-scale small {
                    color: #94a3b8;
                    font-size: 10px;
                }

                /* 统计分析样式 */
                .statistics-content {
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 12px;
                }

                .stat-item {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 8px 12px;
                    background: rgba(59, 130, 246, 0.05);
                    border-radius: 6px;
                    border: 1px solid rgba(59, 130, 246, 0.1);
                }

                .stat-item label {
                    font-size: 12px;
                    font-weight: 500;
                    color: #64748b;
                }

                .stat-item span {
                    font-size: 12px;
                    font-weight: 600;
                    color: #1e293b;
                    font-family: 'Courier New', monospace;
                }

                .btn {
                    padding: 12px 24px;
                    border: none;
                    border-radius: 8px;
                    font-size: 14px;
                    font-weight: 600;
                    cursor: pointer;
                    transition: all 0.3s ease;
                    display: inline-flex;
                    align-items: center;
                    gap: 8px;
                    width: 100%;
                    justify-content: center;
                }

                .btn-primary {
                    background: linear-gradient(135deg, #3b82f6, #0ea5e9);
                    color: white;
                    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
                }

                .btn-primary:hover {
                    transform: translateY(-3px) scale(1.05);
                    box-shadow: 0 12px 35px rgba(59, 130, 246, 0.5);
                    background: linear-gradient(135deg, #2563eb, #0284c7);
                }

                .btn-primary:active {
                    transform: translateY(-1px) scale(0.98);
                    transition: all 0.1s ease;
                }

                .btn-outline {
                    background: transparent;
                    color: #3b82f6;
                    border: 2px solid #3b82f6;
                    position: relative;
                    overflow: hidden;
                }

                .btn-outline::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: -100%;
                    width: 100%;
                    height: 100%;
                    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
                    transition: left 0.5s;
                }

                .btn-outline:hover::before {
                    left: 100%;
                }

                .btn-outline:hover {
                    background: linear-gradient(135deg, #3b82f6, #2563eb);
                    color: white;
                    transform: translateY(-2px) scale(1.05);
                    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
                }

                .btn-outline:active {
                    transform: translateY(0px) scale(0.98);
                    transition: all 0.1s ease;
                }

                /* 文件列表样式 */
                .file-item {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    padding: 8px 12px;
                    background: rgba(255, 255, 255, 0.8);
                    border-radius: 6px;
                    margin-bottom: 6px;
                    border-left: 4px solid;
                    font-size: 12px;
                }

                .file-item.dataset-1 {
                    border-left-color: #3b82f6;
                }

                .file-item.dataset-2 {
                    border-left-color: #ef4444;
                }

                .file-item.dataset-3 {
                    border-left-color: #10b981;
                }

                .file-item-info {
                    flex: 1;
                    display: flex;
                    align-items: center;
                    gap: 8px;
                }

                .file-item-color {
                    width: 12px;
                    height: 12px;
                    border-radius: 50%;
                    flex-shrink: 0;
                }

                .file-item-name {
                    font-weight: 500;
                    color: #1e293b;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }

                .file-item-remove {
                    background: none;
                    border: none;
                    color: #64748b;
                    cursor: pointer;
                    padding: 4px;
                    border-radius: 4px;
                    transition: all 0.2s ease;
                }

                .file-item-remove:hover {
                    background: rgba(239, 68, 68, 0.1);
                    color: #ef4444;
                }
            </style>

            <div class="content-layout">
                <!-- 左侧控制面板 -->
                <div class="control-panel">
                    <!-- 导入数据卡片 -->
                    <div class="control-card">
                        <h3>
                            <i class="fas fa-upload"></i>
                            导入数据
                        </h3>
                        <div class="import-options">
                            <div class="import-option" id="fileImport">
                                <i class="fas fa-file-upload"></i>
                                <h4>多文件上传</h4>
                                <p>最多同时上传3个文件进行对比</p>
                                <input type="file" id="fileInput" accept=".csv,.txt,.json,.xlsx,.xls" multiple style="display: none;">
                            </div>
                            <div class="import-option" id="historyData">
                                <i class="fas fa-history"></i>
                                <h4>历史数据</h4>
                                <p>查看之前导入的数据</p>
                            </div>
                        </div>

                        <!-- 已上传文件列表 -->
                        <div id="uploadedFilesList" style="display: none; margin-top: 15px;">
                            <h4 style="font-size: 14px; margin-bottom: 10px; color: #1e293b;">已上传文件:</h4>
                            <div id="fileListContainer"></div>
                            <div style="margin-top: 10px; display: flex; gap: 8px;">
                                <button class="btn btn-primary" id="compareFilesBtn" style="flex: 1; font-size: 12px; padding: 8px 12px;">
                                    <i class="fas fa-chart-line"></i>
                                    对比显示
                                </button>
                                <button class="btn btn-outline" id="clearFilesBtn" style="flex: 1; font-size: 12px; padding: 8px 12px;">
                                    <i class="fas fa-trash"></i>
                                    清空
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 采样频率配置卡片 -->
                    <div class="control-card">
                        <h3>
                            <i class="fas fa-cog"></i>
                            采样频率配置
                        </h3>
                        <div class="control-group">
                            <label>采样频率: <span id="globalSampleRateValue">1MHz</span></label>
                            <input type="range" id="globalSampleRateSlider" min="0" max="7" step="0.1" value="3.3">
                            <div class="freq-scale">
                                <small>1Hz - 10MHz (对数刻度)</small>
                            </div>
                        </div>
                        <div class="parameter-info">
                            <small><i class="fas fa-info-circle"></i> 适用于导入数据和示例数据的采样频率设置</small>
                        </div>
                    </div>

                    <!-- 加载示例数据卡片 -->
                    <div class="control-card sample-data-card">
                        <h3>
                            <i class="fas fa-database"></i>
                            加载示例数据
                        </h3>
                        <div class="sample-grid">
                            <div class="sample-item active" data-type="sine">
                                <i class="fas fa-wave-square"></i>
                                <span>正弦波</span>
                            </div>
                            <div class="sample-item" data-type="cosine">
                                <i class="fas fa-chart-line"></i>
                                <span>余弦波</span>
                            </div>
                            <div class="sample-item" data-type="square">
                                <i class="fas fa-square"></i>
                                <span>方波</span>
                            </div>
                            <div class="sample-item" data-type="triangle">
                                <i class="fas fa-play"></i>
                                <span>三角波</span>
                            </div>
                            <div class="sample-item" data-type="sawtooth">
                                <i class="fas fa-chart-area"></i>
                                <span>锯齿波</span>
                            </div>
                            <div class="sample-item" data-type="pulse">
                                <i class="fas fa-signal"></i>
                                <span>脉冲波</span>
                            </div>
                        </div>

                        <div class="sample-controls">
                            <div class="control-group">
                                <label>频率: <span id="freqValue">1000</span></label>
                                <input type="range" id="freqSlider" min="0" max="6" step="0.1" value="3">
                                <div class="freq-scale">
                                    <small>1Hz - 1MHz (对数刻度)</small>
                                </div>
                            </div>
                            <div class="control-group">
                                <label>幅度: <span id="ampValue">1.0</span></label>
                                <input type="range" id="ampSlider" min="0.1" max="3" step="0.1" value="1">
                            </div>
                            <div class="control-group">
                                <label>采样点数: <span id="pointsValue">512</span></label>
                                <input type="range" id="pointsSlider" min="1" max="1024" step="1" value="512">
                            </div>


                        </div>

                        <div style="display: flex; gap: 10px;">
                            <button class="btn btn-primary" id="loadSampleBtn" style="flex: 1;">
                                <i class="fas fa-play"></i>
                                加载数据
                            </button>
                            <button class="btn btn-outline" id="exportExcelBtn" style="flex: 1;">
                                <i class="fas fa-file-excel"></i>
                                导出Excel
                            </button>
                        </div>
                    </div>

                    <!-- 噪声配置卡片 -->
                    <div class="control-card" id="noiseCard">
                        <h3>
                            <i class="fas fa-wave-square"></i>
                            噪声配置
                        </h3>

                        <div class="control-group">
                            <div class="noise-toggle-container">
                                <div class="noise-toggle-text-section">
                                    <i class="fas fa-wave-square"></i>
                                    <span>添加噪声干扰</span>
                                </div>
                                <div class="noise-toggle-button-section">
                                    <input type="checkbox" id="noiseCheck" class="noise-toggle-checkbox">
                                    <label for="noiseCheck" class="noise-toggle-label">
                                        <div class="noise-toggle-switch">
                                            <div class="noise-toggle-slider"></div>
                                        </div>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- 噪声配置面板 -->
                        <div id="noiseConfigPanel" class="noise-config-panel" style="display: none;">
                            <div class="noise-types-grid">
                                <div class="noise-type-item" data-type="gaussian">
                                    <input type="checkbox" id="gaussianNoise" class="noise-checkbox">
                                    <label for="gaussianNoise">
                                        <i class="fas fa-wave-square"></i>
                                        <span>高斯噪声</span>
                                    </label>
                                </div>
                                <div class="noise-type-item" data-type="impulse">
                                    <input type="checkbox" id="impulseNoise" class="noise-checkbox">
                                    <label for="impulseNoise">
                                        <i class="fas fa-bolt"></i>
                                        <span>脉冲噪声</span>
                                    </label>
                                </div>
                                <div class="noise-type-item" data-type="white">
                                    <input type="checkbox" id="whiteNoise" class="noise-checkbox">
                                    <label for="whiteNoise">
                                        <i class="fas fa-snowflake"></i>
                                        <span>白噪声</span>
                                    </label>
                                </div>
                                <div class="noise-type-item" data-type="flicker">
                                    <input type="checkbox" id="flickerNoise" class="noise-checkbox">
                                    <label for="flickerNoise">
                                        <i class="fas fa-fire"></i>
                                        <span>闪烁噪声</span>
                                    </label>
                                </div>
                                <div class="noise-type-item" data-type="thermal">
                                    <input type="checkbox" id="thermalNoise" class="noise-checkbox">
                                    <label for="thermalNoise">
                                        <i class="fas fa-thermometer-half"></i>
                                        <span>热噪声</span>
                                    </label>
                                </div>
                                <div class="noise-type-item" data-type="colored">
                                    <input type="checkbox" id="coloredNoise" class="noise-checkbox">
                                    <label for="coloredNoise">
                                        <i class="fas fa-palette"></i>
                                        <span>色噪声</span>
                                    </label>
                                </div>
                            </div>

                            <div class="noise-amplitude-control">
                                <label>噪声幅度: <span id="noiseAmpValue">0.2</span></label>
                                <input type="range" id="noiseAmpSlider" min="0.01" max="1.0" step="0.01" value="0.2">
                                <div class="amplitude-scale">
                                    <small>0.01 - 1.0 (相对于信号幅度)</small>
                                </div>
                            </div>
                        </div>

                        <div style="margin-top: 15px;">
                            <button class="btn btn-primary" id="applyNoiseBtn">
                                <i class="fas fa-plus"></i>
                                应用噪声
                            </button>
                        </div>
                    </div>

                    <!-- 统计分析卡片 -->
                    <div class="control-card" id="statisticsCard" style="display: block;">
                        <h3>
                            <i class="fas fa-calculator"></i>
                            统计分析
                        </h3>
                        <div class="statistics-content">
                            <div class="stat-item">
                                <label>数据点数:</label>
                                <span id="statCount">-</span>
                            </div>
                            <div class="stat-item">
                                <label>均值:</label>
                                <span id="statMean">-</span>
                            </div>
                            <div class="stat-item">
                                <label>方差:</label>
                                <span id="statVariance">-</span>
                            </div>
                            <div class="stat-item">
                                <label>标准差:</label>
                                <span id="statStdDev">-</span>
                            </div>
                            <div class="stat-item">
                                <label>最大值:</label>
                                <span id="statMax">-</span>
                            </div>
                            <div class="stat-item">
                                <label>最小值:</label>
                                <span id="statMin">-</span>
                            </div>
                            <div class="stat-item">
                                <label>峰峰值:</label>
                                <span id="statPeakToPeak">-</span>
                            </div>
                            <div class="stat-item">
                                <label>RMS值:</label>
                                <span id="statRMS">-</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 右侧可视化区域 -->
                <div class="visualization-area">
                    <style>
                        /* 右侧可视化区域样式 */
                        .visualization-area {
                            background: rgba(255, 255, 255, 0.95);
                            backdrop-filter: blur(25px);
                            border-radius: 20px;
                            padding: 25px;
                            box-shadow:
                                0 12px 45px rgba(0, 0, 0, 0.06),
                                0 6px 25px rgba(59, 130, 246, 0.08),
                                inset 0 1px 0 rgba(255, 255, 255, 0.8);
                            border: 1px solid rgba(255, 255, 255, 0.3);
                            overflow: visible;
                            display: flex;
                            flex-direction: column;
                            position: relative;
                            min-width: 1400px;
                            min-height: fit-content;
                            height: fit-content;
                        }

                        .visualization-area::before {
                            content: '';
                            position: absolute;
                            top: 0;
                            left: 0;
                            right: 0;
                            height: 1px;
                            background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.3), transparent);
                        }

                        .visualization-header {
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                            margin-bottom: 10px;
                            padding-bottom: 8px;
                            border-bottom: 1px solid rgba(59, 130, 246, 0.1);
                            flex-shrink: 0;
                            height: 50px;
                            min-height: 50px;
                        }

                        .visualization-title {
                            font-size: 20px;
                            font-weight: 700;
                            color: #1e293b;
                            margin: 0;
                            background: linear-gradient(135deg, #3b82f6, #0ea5e9);
                            -webkit-background-clip: text;
                            -webkit-text-fill-color: transparent;
                            background-clip: text;
                        }

                        .visualization-actions {
                            display: flex;
                            gap: 12px;
                        }

                        .btn-small {
                            padding: 8px 16px;
                            font-size: 12px;
                            width: auto;
                        }

                        /* 图表网格样式 - 3列多行布局 */
                        .charts-grid {
                            display: grid;
                            grid-template-columns: repeat(3, 1fr);
                            grid-auto-rows: 330px;
                            gap: 15px;
                            width: 100%;
                            min-width: 1400px;
                            padding: 5px 15px 30px 15px;
                            align-items: stretch;
                            justify-items: stretch;
                            min-height: min-content;
                        }

                        .chart-card {
                            background: rgba(255, 255, 255, 0.92);
                            border-radius: 16px;
                            padding: 16px;
                            box-shadow:
                                0 8px 30px rgba(0, 0, 0, 0.06),
                                0 4px 15px rgba(59, 130, 246, 0.08),
                                inset 0 1px 0 rgba(255, 255, 255, 0.8);
                            border: 1px solid rgba(255, 255, 255, 0.5);
                            transition: all 0.3s ease;
                            position: relative;
                            overflow: visible;
                            display: flex;
                            flex-direction: column;
                            width: 100%;
                            height: 330px;
                        }

                        .chart-card::before {
                            content: '';
                            position: absolute;
                            top: 0;
                            left: 0;
                            right: 0;
                            height: 1px;
                            background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.2), transparent);
                        }

                        .chart-card:hover {
                            transform: translateY(-8px) scale(1.02);
                            box-shadow:
                                0 20px 50px rgba(0, 0, 0, 0.08),
                                0 6px 20px rgba(59, 130, 246, 0.12),
                                inset 0 1px 0 rgba(255, 255, 255, 0.8);
                            border-color: rgba(255, 255, 255, 0.6);
                        }

                        .chart-card:active {
                            transform: translateY(-3px) scale(0.99);
                            transition: all 0.1s ease;
                        }

                        .chart-card.dragging {
                            cursor: grabbing;
                            transform: scale(1.05);
                            box-shadow:
                                0 25px 70px rgba(0, 0, 0, 0.15),
                                0 10px 30px rgba(59, 130, 246, 0.2);
                            z-index: 1000;
                        }

                        .chart-header {
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                            margin-bottom: 6px;
                            padding-bottom: 4px;
                            border-bottom: 1px solid rgba(59, 130, 246, 0.1);
                            flex-shrink: 0;
                            height: 40px;
                            min-height: 40px;
                        }

                        .chart-title {
                            display: flex;
                            align-items: center;
                            gap: 6px;
                            font-size: 14px;
                            font-weight: 600;
                            color: #1e293b;
                            margin: 0;
                        }

                        .chart-title i {
                            color: #3b82f6;
                            font-size: 16px;
                        }

                        .chart-actions {
                            display: flex;
                            gap: 6px;
                            flex-wrap: wrap;
                        }

                        .chart-btn {
                            width: 28px;
                            height: 28px;
                            border: none;
                            background: rgba(59, 130, 246, 0.1);
                            color: #3b82f6;
                            border-radius: 6px;
                            cursor: pointer;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            transition: all 0.3s ease;
                            font-size: 11px;
                        }

                        .chart-btn:hover {
                            background: rgba(59, 130, 246, 0.2);
                            transform: scale(1.1);
                        }

                        .operation-select {
                            padding: 4px 8px;
                            border: 1px solid rgba(59, 130, 246, 0.3);
                            border-radius: 4px;
                            background: rgba(255, 255, 255, 0.9);
                            color: #3b82f6;
                            font-size: 11px;
                            font-weight: 500;
                            cursor: pointer;
                            transition: all 0.3s ease;
                            margin-right: 5px;
                        }

                        .operation-select:hover {
                            background: rgba(59, 130, 246, 0.1);
                            border-color: rgba(59, 130, 246, 0.5);
                        }

                        .operation-select:focus {
                            outline: none;
                            border-color: #3b82f6;
                            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
                        }

                        .chart-body {
                            flex: 1;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(14, 165, 233, 0.05) 100%);
                            border-radius: 8px;
                            position: relative;
                            width: 100%;
                            height: 260px;
                            overflow: visible;
                            padding-bottom: 10px;
                        }

                        .chart-placeholder {
                            text-align: center;
                            color: #64748b;
                        }

                        .chart-placeholder i {
                            font-size: 48px;
                            color: #3b82f6;
                            margin-bottom: 15px;
                            display: block;
                        }

                        .chart-placeholder h4 {
                            font-size: 16px;
                            font-weight: 600;
                            color: #1e293b;
                            margin: 0 0 8px 0;
                        }

                        .chart-placeholder p {
                            font-size: 14px;
                            color: #64748b;
                            margin: 0 0 8px 0;
                        }

                        .chart-placeholder small {
                            font-size: 12px;
                            color: #94a3b8;
                            font-style: italic;
                        }

                        .chart-canvas {
                            width: 100% !important;
                            height: 100% !important;
                            max-width: calc(100% - 10px) !important;
                            max-height: calc(100% - 10px) !important;
                            object-fit: contain;
                            display: block;
                        }

                        /* 自定义滚动条样式 */
                        .visualization-area::-webkit-scrollbar,
                        .control-panel::-webkit-scrollbar {
                            width: 8px;
                        }

                        .visualization-area::-webkit-scrollbar-track,
                        .control-panel::-webkit-scrollbar-track {
                            background: rgba(148, 163, 184, 0.1);
                            border-radius: 4px;
                        }

                        .visualization-area::-webkit-scrollbar-thumb,
                        .control-panel::-webkit-scrollbar-thumb {
                            background: rgba(59, 130, 246, 0.3);
                            border-radius: 4px;
                            transition: background 0.3s ease;
                        }

                        .visualization-area::-webkit-scrollbar-thumb:hover,
                        .control-panel::-webkit-scrollbar-thumb:hover {
                            background: rgba(59, 130, 246, 0.5);
                        }

                        /* 响应式设计 */
                        @media (max-width: 1400px) {
                            .content-layout {
                                grid-template-columns: 300px 1fr;
                                gap: 15px;
                            }

                            .main-content {
                                padding: 12px 15px 40px 15px;
                            }

                            .main-container {
                                grid-template-columns: 240px 1fr;
                            }
                        }

                        @media (max-width: 1200px) {
                            .content-layout {
                                grid-template-columns: 1fr;
                                gap: 20px;
                            }

                            .control-panel {
                                flex-direction: row;
                                overflow-x: auto;
                                gap: 15px;
                            }

                            .control-card {
                                min-width: 280px;
                                flex-shrink: 0;
                            }

                            .charts-grid {
                                grid-template-columns: repeat(2, 1fr);
                                grid-auto-rows: 330px;
                                min-width: auto;
                                gap: 15px;
                            }

                            .visualization-area {
                                min-width: auto;
                                width: 100%;
                                height: auto;
                                max-height: none;
                            }

                            .chart-card {
                                height: 330px;
                            }


                        }

                        @media (max-width: 768px) {
                            .main-container {
                                grid-template-columns: 1fr;
                            }

                            .sidebar {
                                display: none;
                            }

                            .main-content {
                                padding: 15px 15px 50px 15px;
                            }

                            .control-card {
                                min-width: 250px;
                                padding: 18px;
                            }

                            .charts-grid {
                                grid-template-columns: 1fr;
                                grid-auto-rows: 330px;
                                gap: 15px;
                                margin-bottom: 30px;
                                min-width: auto;
                            }
                        }
                    </style>

                    <div class="visualization-header">
                        <h2 class="visualization-title">可视化展示</h2>
                        <div class="visualization-actions">
                            <button class="btn btn-outline btn-small" id="exportAllBtn">
                                <i class="fas fa-download"></i>
                                导出全部
                            </button>
                            <button class="btn btn-primary btn-small" id="refreshAllBtn">
                                <i class="fas fa-sync-alt"></i>
                                刷新数据
                            </button>
                        </div>
                    </div>

                    <!-- 图表网格 -->
                    <div class="charts-grid">
                        <!-- 时域波形图 -->
                        <div class="chart-card" id="timeChart">
                            <div class="chart-header">
                                <h3 class="chart-title">
                                    <i class="fas fa-wave-square"></i>
                                    时域波形
                                </h3>
                                <div class="chart-actions">
                                    <button class="chart-btn" data-action="zoom-x-in" data-chart="time" title="横轴放大">
                                        <i class="fas fa-search-plus"></i>
                                    </button>
                                    <button class="chart-btn" data-action="zoom-x-out" data-chart="time" title="横轴缩小">
                                        <i class="fas fa-search-minus"></i>
                                    </button>
                                    <button class="chart-btn" data-action="zoom-y-in" data-chart="time" title="纵轴放大">
                                        <i class="fas fa-arrows-alt-v"></i>
                                    </button>
                                    <button class="chart-btn" data-action="zoom-y-out" data-chart="time" title="纵轴缩小">
                                        <i class="fas fa-compress-arrows-alt"></i>
                                    </button>
                                    <button class="chart-btn" data-action="export" data-chart="time" title="导出PNG">
                                        <i class="fas fa-image"></i>
                                    </button>
                                    <button class="chart-btn" data-action="export-jpg" data-chart="time" title="导出JPG">
                                        <i class="fas fa-file-image"></i>
                                    </button>
                                    <button class="chart-btn" data-action="export-excel" data-chart="time" title="导出Excel">
                                        <i class="fas fa-file-excel"></i>
                                    </button>
                                    <button class="chart-btn" data-action="auto" data-chart="time" title="自动缩放">
                                        <i class="fas fa-magic"></i>
                                    </button>
                                    <button class="chart-btn" data-action="fullscreen" data-chart="time" title="全屏">
                                        <i class="fas fa-expand"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="chart-body">
                                <canvas id="timeChartCanvas" class="chart-canvas"></canvas>
                            </div>
                        </div>

                        <!-- 频域分析图 -->
                        <div class="chart-card" id="frequencyChart">
                            <div class="chart-header">
                                <h3 class="chart-title">
                                    <i class="fas fa-chart-area"></i>
                                    频域分析
                                </h3>
                                <div class="chart-actions">
                                    <button class="chart-btn" data-action="zoom-x-in" data-chart="frequency" title="横轴放大">
                                        <i class="fas fa-search-plus"></i>
                                    </button>
                                    <button class="chart-btn" data-action="zoom-x-out" data-chart="frequency" title="横轴缩小">
                                        <i class="fas fa-search-minus"></i>
                                    </button>
                                    <button class="chart-btn" data-action="zoom-y-in" data-chart="frequency" title="纵轴放大">
                                        <i class="fas fa-arrows-alt-v"></i>
                                    </button>
                                    <button class="chart-btn" data-action="zoom-y-out" data-chart="frequency" title="纵轴缩小">
                                        <i class="fas fa-compress-arrows-alt"></i>
                                    </button>
                                    <button class="chart-btn" data-action="export" data-chart="frequency" title="导出PNG">
                                        <i class="fas fa-image"></i>
                                    </button>
                                    <button class="chart-btn" data-action="export-jpg" data-chart="frequency" title="导出JPG">
                                        <i class="fas fa-file-image"></i>
                                    </button>
                                    <button class="chart-btn" data-action="export-excel" data-chart="frequency" title="导出Excel">
                                        <i class="fas fa-file-excel"></i>
                                    </button>
                                    <button class="chart-btn" data-action="auto" data-chart="frequency" title="自动缩放">
                                        <i class="fas fa-magic"></i>
                                    </button>
                                    <button class="chart-btn" data-action="fullscreen" data-chart="frequency" title="全屏">
                                        <i class="fas fa-expand"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="chart-body">
                                <canvas id="frequencyChartCanvas" class="chart-canvas"></canvas>
                            </div>
                        </div>

                        <!-- 数据分布图 -->
                        <div class="chart-card" id="barChart">
                            <div class="chart-header">
                                <h3 class="chart-title">
                                    <i class="fas fa-chart-bar"></i>
                                    数据分布
                                </h3>
                                <div class="chart-actions">
                                    <button class="chart-btn" data-action="zoom-x-in" data-chart="bar" title="横轴放大">
                                        <i class="fas fa-search-plus"></i>
                                    </button>
                                    <button class="chart-btn" data-action="zoom-x-out" data-chart="bar" title="横轴缩小">
                                        <i class="fas fa-search-minus"></i>
                                    </button>
                                    <button class="chart-btn" data-action="zoom-y-in" data-chart="bar" title="纵轴放大">
                                        <i class="fas fa-arrows-alt-v"></i>
                                    </button>
                                    <button class="chart-btn" data-action="zoom-y-out" data-chart="bar" title="纵轴缩小">
                                        <i class="fas fa-compress-arrows-alt"></i>
                                    </button>
                                    <button class="chart-btn" data-action="export" data-chart="bar" title="导出PNG">
                                        <i class="fas fa-image"></i>
                                    </button>
                                    <button class="chart-btn" data-action="export-jpg" data-chart="bar" title="导出JPG">
                                        <i class="fas fa-file-image"></i>
                                    </button>
                                    <button class="chart-btn" data-action="export-excel" data-chart="bar" title="导出Excel">
                                        <i class="fas fa-file-excel"></i>
                                    </button>
                                    <button class="chart-btn" data-action="auto" data-chart="bar" title="自动缩放">
                                        <i class="fas fa-magic"></i>
                                    </button>
                                    <button class="chart-btn" data-action="fullscreen" data-chart="bar" title="全屏">
                                        <i class="fas fa-expand"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="chart-body">
                                <canvas id="barChartCanvas" class="chart-canvas"></canvas>
                            </div>
                        </div>

                        <!-- 包络线图 -->
                        <div class="chart-card" id="envelopeChart">
                            <div class="chart-header">
                                <h3 class="chart-title">
                                    <i class="fas fa-mountain"></i>
                                    包络线图
                                </h3>
                                <div class="chart-actions">
                                    <button class="chart-btn" data-action="zoom-x-in" data-chart="envelope" title="横轴放大">
                                        <i class="fas fa-search-plus"></i>
                                    </button>
                                    <button class="chart-btn" data-action="zoom-x-out" data-chart="envelope" title="横轴缩小">
                                        <i class="fas fa-search-minus"></i>
                                    </button>
                                    <button class="chart-btn" data-action="zoom-y-in" data-chart="envelope" title="纵轴放大">
                                        <i class="fas fa-arrows-alt-v"></i>
                                    </button>
                                    <button class="chart-btn" data-action="zoom-y-out" data-chart="envelope" title="纵轴缩小">
                                        <i class="fas fa-compress-arrows-alt"></i>
                                    </button>
                                    <button class="chart-btn" data-action="export" data-chart="envelope" title="导出PNG">
                                        <i class="fas fa-image"></i>
                                    </button>
                                    <button class="chart-btn" data-action="export-jpg" data-chart="envelope" title="导出JPG">
                                        <i class="fas fa-file-image"></i>
                                    </button>
                                    <button class="chart-btn" data-action="export-excel" data-chart="envelope" title="导出Excel">
                                        <i class="fas fa-file-excel"></i>
                                    </button>
                                    <button class="chart-btn" data-action="auto" data-chart="envelope" title="自动缩放">
                                        <i class="fas fa-magic"></i>
                                    </button>
                                    <button class="chart-btn" data-action="fullscreen" data-chart="envelope" title="全屏">
                                        <i class="fas fa-expand"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="chart-body">
                                <canvas id="envelopeChartCanvas" class="chart-canvas"></canvas>
                            </div>
                        </div>

                        <!-- 功率谱密度图 -->
                        <div class="chart-card" id="psdChart">
                            <div class="chart-header">
                                <h3 class="chart-title">
                                    <i class="fas fa-bolt"></i>
                                    功率谱密度
                                </h3>
                                <div class="chart-actions">
                                    <button class="chart-btn" data-action="zoom-x-in" data-chart="psd" title="横轴放大">
                                        <i class="fas fa-search-plus"></i>
                                    </button>
                                    <button class="chart-btn" data-action="zoom-x-out" data-chart="psd" title="横轴缩小">
                                        <i class="fas fa-search-minus"></i>
                                    </button>
                                    <button class="chart-btn" data-action="zoom-y-in" data-chart="psd" title="纵轴放大">
                                        <i class="fas fa-arrows-alt-v"></i>
                                    </button>
                                    <button class="chart-btn" data-action="zoom-y-out" data-chart="psd" title="纵轴缩小">
                                        <i class="fas fa-compress-arrows-alt"></i>
                                    </button>
                                    <button class="chart-btn" data-action="export" data-chart="psd" title="导出PNG">
                                        <i class="fas fa-image"></i>
                                    </button>
                                    <button class="chart-btn" data-action="export-jpg" data-chart="psd" title="导出JPG">
                                        <i class="fas fa-file-image"></i>
                                    </button>
                                    <button class="chart-btn" data-action="export-excel" data-chart="psd" title="导出Excel">
                                        <i class="fas fa-file-excel"></i>
                                    </button>
                                    <button class="chart-btn" data-action="auto" data-chart="psd" title="自动缩放">
                                        <i class="fas fa-magic"></i>
                                    </button>
                                    <button class="chart-btn" data-action="fullscreen" data-chart="psd" title="全屏">
                                        <i class="fas fa-expand"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="chart-body">
                                <canvas id="psdChartCanvas" class="chart-canvas"></canvas>
                            </div>
                        </div>

                        <!-- 瞬时相位图 -->
                        <div class="chart-card" id="phaseChart">
                            <div class="chart-header">
                                <h3 class="chart-title">
                                    <i class="fas fa-sync-alt"></i>
                                    瞬时相位
                                </h3>
                                <div class="chart-actions">
                                    <button class="chart-btn" data-action="zoom-x-in" data-chart="phase" title="横轴放大">
                                        <i class="fas fa-search-plus"></i>
                                    </button>
                                    <button class="chart-btn" data-action="zoom-x-out" data-chart="phase" title="横轴缩小">
                                        <i class="fas fa-search-minus"></i>
                                    </button>
                                    <button class="chart-btn" data-action="zoom-y-in" data-chart="phase" title="纵轴放大">
                                        <i class="fas fa-arrows-alt-v"></i>
                                    </button>
                                    <button class="chart-btn" data-action="zoom-y-out" data-chart="phase" title="纵轴缩小">
                                        <i class="fas fa-compress-arrows-alt"></i>
                                    </button>
                                    <button class="chart-btn" data-action="export" data-chart="phase" title="导出PNG">
                                        <i class="fas fa-image"></i>
                                    </button>
                                    <button class="chart-btn" data-action="export-jpg" data-chart="phase" title="导出JPG">
                                        <i class="fas fa-file-image"></i>
                                    </button>
                                    <button class="chart-btn" data-action="export-excel" data-chart="phase" title="导出Excel">
                                        <i class="fas fa-file-excel"></i>
                                    </button>
                                    <button class="chart-btn" data-action="auto" data-chart="phase" title="自动缩放">
                                        <i class="fas fa-magic"></i>
                                    </button>
                                    <button class="chart-btn" data-action="fullscreen" data-chart="phase" title="全屏">
                                        <i class="fas fa-expand"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="chart-body">
                                <canvas id="phaseChartCanvas" class="chart-canvas"></canvas>
                            </div>
                        </div>

                        <!-- 信号运算图 -->
                        <div class="chart-card" id="operationChart">
                            <div class="chart-header">
                                <h3 class="chart-title">
                                    <i class="fas fa-calculator"></i>
                                    信号运算
                                </h3>
                                <div class="chart-actions">
                                    <select id="operationSelect" class="operation-select">
                                        <option value="add">加法</option>
                                        <option value="subtract">减法</option>
                                        <option value="multiply">乘法</option>
                                        <option value="divide">除法</option>
                                    </select>
                                    <button class="chart-btn" data-action="zoom-x-in" data-chart="operation" title="横轴放大">
                                        <i class="fas fa-search-plus"></i>
                                    </button>
                                    <button class="chart-btn" data-action="zoom-x-out" data-chart="operation" title="横轴缩小">
                                        <i class="fas fa-search-minus"></i>
                                    </button>
                                    <button class="chart-btn" data-action="zoom-y-in" data-chart="operation" title="纵轴放大">
                                        <i class="fas fa-arrows-alt-v"></i>
                                    </button>
                                    <button class="chart-btn" data-action="zoom-y-out" data-chart="operation" title="纵轴缩小">
                                        <i class="fas fa-compress-arrows-alt"></i>
                                    </button>
                                    <button class="chart-btn" data-action="export" data-chart="operation" title="导出PNG">
                                        <i class="fas fa-image"></i>
                                    </button>
                                    <button class="chart-btn" data-action="export-jpg" data-chart="operation" title="导出JPG">
                                        <i class="fas fa-file-image"></i>
                                    </button>
                                    <button class="chart-btn" data-action="export-excel" data-chart="operation" title="导出Excel">
                                        <i class="fas fa-file-excel"></i>
                                    </button>
                                    <button class="chart-btn" data-action="auto" data-chart="operation" title="自动缩放">
                                        <i class="fas fa-magic"></i>
                                    </button>
                                    <button class="chart-btn" data-action="fullscreen" data-chart="operation" title="全屏">
                                        <i class="fas fa-expand"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="chart-body">
                                <canvas id="operationChartCanvas" class="chart-canvas"></canvas>
                            </div>
                        </div>

                        <!-- 相关图 -->
                        <div class="chart-card" id="correlationChart">
                            <div class="chart-header">
                                <h3 class="chart-title">
                                    <i class="fas fa-link"></i>
                                    相关分析
                                </h3>
                                <div class="chart-actions">
                                    <select id="correlationSelect" class="operation-select">
                                        <option value="autocorr">自相关</option>
                                        <option value="crosscorr">互相关</option>
                                    </select>
                                    <button class="chart-btn" data-action="zoom-x-in" data-chart="correlation" title="横轴放大">
                                        <i class="fas fa-search-plus"></i>
                                    </button>
                                    <button class="chart-btn" data-action="zoom-x-out" data-chart="correlation" title="横轴缩小">
                                        <i class="fas fa-search-minus"></i>
                                    </button>
                                    <button class="chart-btn" data-action="zoom-y-in" data-chart="correlation" title="纵轴放大">
                                        <i class="fas fa-arrows-alt-v"></i>
                                    </button>
                                    <button class="chart-btn" data-action="zoom-y-out" data-chart="correlation" title="纵轴缩小">
                                        <i class="fas fa-compress-arrows-alt"></i>
                                    </button>
                                    <button class="chart-btn" data-action="export" data-chart="correlation" title="导出PNG">
                                        <i class="fas fa-image"></i>
                                    </button>
                                    <button class="chart-btn" data-action="export-jpg" data-chart="correlation" title="导出JPG">
                                        <i class="fas fa-file-image"></i>
                                    </button>
                                    <button class="chart-btn" data-action="export-excel" data-chart="correlation" title="导出Excel">
                                        <i class="fas fa-file-excel"></i>
                                    </button>
                                    <button class="chart-btn" data-action="auto" data-chart="correlation" title="自动缩放">
                                        <i class="fas fa-magic"></i>
                                    </button>
                                    <button class="chart-btn" data-action="fullscreen" data-chart="correlation" title="全屏">
                                        <i class="fas fa-expand"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="chart-body">
                                <canvas id="correlationChartCanvas" class="chart-canvas"></canvas>
                            </div>
                        </div>

                        <!-- 累积分布函数图 -->
                        <div class="chart-card" id="cdfChart">
                            <div class="chart-header">
                                <h3 class="chart-title">
                                    <i class="fas fa-chart-line"></i>
                                    累积分布函数
                                </h3>
                                <div class="chart-actions">
                                    <button class="chart-btn" data-action="zoom-x-in" data-chart="cdf" title="横轴放大">
                                        <i class="fas fa-search-plus"></i>
                                    </button>
                                    <button class="chart-btn" data-action="zoom-x-out" data-chart="cdf" title="横轴缩小">
                                        <i class="fas fa-search-minus"></i>
                                    </button>
                                    <button class="chart-btn" data-action="zoom-y-in" data-chart="cdf" title="纵轴放大">
                                        <i class="fas fa-arrows-alt-v"></i>
                                    </button>
                                    <button class="chart-btn" data-action="zoom-y-out" data-chart="cdf" title="纵轴缩小">
                                        <i class="fas fa-compress-arrows-alt"></i>
                                    </button>
                                    <button class="chart-btn" data-action="export" data-chart="cdf" title="导出PNG">
                                        <i class="fas fa-image"></i>
                                    </button>
                                    <button class="chart-btn" data-action="export-jpg" data-chart="cdf" title="导出JPG">
                                        <i class="fas fa-file-image"></i>
                                    </button>
                                    <button class="chart-btn" data-action="export-excel" data-chart="cdf" title="导出Excel">
                                        <i class="fas fa-file-excel"></i>
                                    </button>
                                    <button class="chart-btn" data-action="auto" data-chart="cdf" title="自动缩放">
                                        <i class="fas fa-magic"></i>
                                    </button>
                                    <button class="chart-btn" data-action="fullscreen" data-chart="cdf" title="全屏">
                                        <i class="fas fa-expand"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="chart-body">
                                <canvas id="cdfChartCanvas" class="chart-canvas"></canvas>
                            </div>
                        </div>

                        <!-- 小波变换图 -->
                        <div class="chart-card" id="waveletChart">
                            <div class="chart-header">
                                <h3 class="chart-title">
                                    <i class="fas fa-water"></i>
                                    小波变换
                                </h3>
                                <div class="chart-actions">
                                    <select id="waveletSelect" class="operation-select">
                                        <option value="morlet">Morlet小波</option>
                                        <option value="mexican">Mexican Hat</option>
                                        <option value="daubechies">Daubechies</option>
                                    </select>
                                    <button class="chart-btn" data-action="zoom-x-in" data-chart="wavelet" title="横轴放大">
                                        <i class="fas fa-search-plus"></i>
                                    </button>
                                    <button class="chart-btn" data-action="zoom-x-out" data-chart="wavelet" title="横轴缩小">
                                        <i class="fas fa-search-minus"></i>
                                    </button>
                                    <button class="chart-btn" data-action="zoom-y-in" data-chart="wavelet" title="纵轴放大">
                                        <i class="fas fa-arrows-alt-v"></i>
                                    </button>
                                    <button class="chart-btn" data-action="zoom-y-out" data-chart="wavelet" title="纵轴缩小">
                                        <i class="fas fa-compress-arrows-alt"></i>
                                    </button>
                                    <button class="chart-btn" data-action="export" data-chart="wavelet" title="导出PNG">
                                        <i class="fas fa-image"></i>
                                    </button>
                                    <button class="chart-btn" data-action="export-jpg" data-chart="wavelet" title="导出JPG">
                                        <i class="fas fa-file-image"></i>
                                    </button>
                                    <button class="chart-btn" data-action="export-excel" data-chart="wavelet" title="导出Excel">
                                        <i class="fas fa-file-excel"></i>
                                    </button>
                                    <button class="chart-btn" data-action="auto" data-chart="wavelet" title="自动缩放">
                                        <i class="fas fa-magic"></i>
                                    </button>
                                    <button class="chart-btn" data-action="fullscreen" data-chart="wavelet" title="全屏">
                                        <i class="fas fa-expand"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="chart-body">
                                <canvas id="waveletChartCanvas" class="chart-canvas"></canvas>
                            </div>
                        </div>

                        <!-- 极坐标图 -->
                        <div class="chart-card" id="polarChart">
                            <div class="chart-header">
                                <h3 class="chart-title">
                                    <i class="fas fa-circle-notch"></i>
                                    极坐标图
                                </h3>
                                <div class="chart-actions">
                                    <button class="chart-btn" data-action="zoom-x-in" data-chart="polar" title="横轴放大">
                                        <i class="fas fa-search-plus"></i>
                                    </button>
                                    <button class="chart-btn" data-action="zoom-x-out" data-chart="polar" title="横轴缩小">
                                        <i class="fas fa-search-minus"></i>
                                    </button>
                                    <button class="chart-btn" data-action="zoom-y-in" data-chart="polar" title="纵轴放大">
                                        <i class="fas fa-arrows-alt-v"></i>
                                    </button>
                                    <button class="chart-btn" data-action="zoom-y-out" data-chart="polar" title="纵轴缩小">
                                        <i class="fas fa-compress-arrows-alt"></i>
                                    </button>
                                    <button class="chart-btn" data-action="export" data-chart="polar" title="导出PNG">
                                        <i class="fas fa-image"></i>
                                    </button>
                                    <button class="chart-btn" data-action="export-jpg" data-chart="polar" title="导出JPG">
                                        <i class="fas fa-file-image"></i>
                                    </button>
                                    <button class="chart-btn" data-action="export-excel" data-chart="polar" title="导出Excel">
                                        <i class="fas fa-file-excel"></i>
                                    </button>
                                    <button class="chart-btn" data-action="auto" data-chart="polar" title="自动缩放">
                                        <i class="fas fa-magic"></i>
                                    </button>
                                    <button class="chart-btn" data-action="fullscreen" data-chart="polar" title="全屏">
                                        <i class="fas fa-expand"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="chart-body">
                                <canvas id="polarChartCanvas" class="chart-canvas"></canvas>
                            </div>
                        </div>

                        <!-- 矢量图 -->
                        <div class="chart-card" id="vectorChart">
                            <div class="chart-header">
                                <h3 class="chart-title">
                                    <i class="fas fa-arrows-alt"></i>
                                    矢量图
                                </h3>
                                <div class="chart-actions">
                                    <button class="chart-btn" data-action="zoom-x-in" data-chart="vector" title="横轴放大">
                                        <i class="fas fa-search-plus"></i>
                                    </button>
                                    <button class="chart-btn" data-action="zoom-x-out" data-chart="vector" title="横轴缩小">
                                        <i class="fas fa-search-minus"></i>
                                    </button>
                                    <button class="chart-btn" data-action="zoom-y-in" data-chart="vector" title="纵轴放大">
                                        <i class="fas fa-arrows-alt-v"></i>
                                    </button>
                                    <button class="chart-btn" data-action="zoom-y-out" data-chart="vector" title="纵轴缩小">
                                        <i class="fas fa-compress-arrows-alt"></i>
                                    </button>
                                    <button class="chart-btn" data-action="export" data-chart="vector" title="导出PNG">
                                        <i class="fas fa-image"></i>
                                    </button>
                                    <button class="chart-btn" data-action="export-jpg" data-chart="vector" title="导出JPG">
                                        <i class="fas fa-file-image"></i>
                                    </button>
                                    <button class="chart-btn" data-action="export-excel" data-chart="vector" title="导出Excel">
                                        <i class="fas fa-file-excel"></i>
                                    </button>
                                    <button class="chart-btn" data-action="auto" data-chart="vector" title="自动缩放">
                                        <i class="fas fa-magic"></i>
                                    </button>
                                    <button class="chart-btn" data-action="fullscreen" data-chart="vector" title="全屏">
                                        <i class="fas fa-expand"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="chart-body">
                                <canvas id="vectorChartCanvas" class="chart-canvas"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- JavaScript功能实现 -->
    <script>
        // 全局变量
        let currentData = null;
        let multipleDataSets = []; // 存储多个数据集
        let charts = {};
        let historyData = JSON.parse(localStorage.getItem('dataHistory') || '[]');

        // 页面加载完成后初始化导航栏交互
        // 注释掉重复调用，在initializeApp中统一初始化
        // document.addEventListener('DOMContentLoaded', function() {
        //     initNavigation();
        // });

        // 初始化导航栏交互
        // 删除重复的函数，使用下面的initNavigation函数
        let currentWaveType = 'sine';
        let isDragging = false;
        let dragStartX = 0;
        let chartPanOffset = { x: 0, y: 0 };

        // 数据集颜色配置
        const dataSetColors = [
            { border: '#3b82f6', background: 'rgba(59, 130, 246, 0.1)', name: '数据集1' },
            { border: '#ef4444', background: 'rgba(239, 68, 68, 0.1)', name: '数据集2' },
            { border: '#10b981', background: 'rgba(16, 185, 129, 0.1)', name: '数据集3' }
        ];

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM加载完成，开始初始化...');

            // 设置一个最大加载时间，防止卡住
            const maxLoadTime = setTimeout(() => {
                console.warn('强制隐藏加载动画（超时）');
                const loader = document.getElementById('pageLoader');
                if (loader) {
                    loader.classList.add('loaded');
                }
            }, 5000); // 5秒超时

            try {
                // 初始化所有功能
                initializeApp();
                initParticles();
                initChartInteractions();

                // 初始化成功后隐藏加载动画
                setTimeout(() => {
                    console.log('初始化完成，隐藏加载动画');
                    const loader = document.getElementById('pageLoader');
                    if (loader) {
                        loader.classList.add('loaded');
                    }
                    clearTimeout(maxLoadTime); // 清除超时定时器
                }, 1000); // 减少到1秒

            } catch (error) {
                console.error('页面初始化失败:', error);
                // 即使出错也要隐藏加载动画
                const loader = document.getElementById('pageLoader');
                if (loader) {
                    loader.classList.add('loaded');
                }
                clearTimeout(maxLoadTime);
                showNotification('页面初始化失败: ' + error.message, 'error');
            }
        });

        // 防抖函数
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        // 窗口大小改变时重新调整高度（使用防抖）
        window.addEventListener('resize', debounce(() => {
            adjustVisualizationHeight();
            syncControlPanelHeight();
        }, 200));

        // 检查Chart.js是否加载成功
        function checkChartJS() {
            console.log('检查Chart.js库状态...');

            if (typeof Chart !== 'undefined') {
                console.log('✓ Chart.js库已成功加载');
                return true;
            } else {
                console.error('✗ Chart.js库未加载！');
                showNotification('图表库加载失败，正在尝试重新加载...', 'warning');

                // 尝试动态加载备用Chart.js
                const script = document.createElement('script');
                script.src = 'https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js';
                script.onload = function() {
                    console.log('✓ 备用Chart.js加载成功');
                    showNotification('图表库已重新加载', 'success');
                    // 重新初始化
                    setTimeout(() => {
                        initializeAppCore();
                    }, 500);
                };
                script.onerror = function() {
                    console.error('✗ 备用Chart.js也无法加载');
                    showNotification('图表库加载失败，图表功能可能无法使用', 'error');
                    // 即使Chart.js失败也继续初始化其他功能
                    initializeAppCore();
                };
                document.head.appendChild(script);
                return false;
            }
        }

        // 通用图表检查函数
        function checkChartAvailability(chartName) {
            if (typeof Chart === 'undefined') {
                console.error(`Chart.js未加载，无法更新${chartName}图表`);
                showNotification(`图表库未加载，无法显示${chartName}图表`, 'error');
                return false;
            }
            return true;
        }

        // 通用画布检查函数
        function getChartCanvas(canvasId, chartName) {
            const canvas = document.getElementById(canvasId);
            if (!canvas) {
                console.error(`找不到${chartName}图表画布元素: ${canvasId}`);
                showNotification(`${chartName}图表画布未找到`, 'error');
                return null;
            }
            return canvas;
        }

        // 核心应用初始化
        function initializeAppCore() {
            console.log('开始初始化应用核心功能...');

            try {
                initNavigation();
                initDataImport();
                initSampleData();
                initNoiseConfig();
                initChartActions();

                // 只有在Chart.js可用时才加载默认数据
                if (typeof Chart !== 'undefined') {
                    loadDefaultData();
                } else {
                    console.warn('Chart.js不可用，跳过默认数据加载');
                    showNotification('图表库不可用，请刷新页面重试', 'error');
                }

                // 延迟调整高度，确保DOM完全渲染
                setTimeout(() => {
                    adjustVisualizationHeight();
                    syncControlPanelHeight();
                }, 200);

                observeStatisticsCard(); // 监听统计分析卡片变化

                console.log('✓ 应用核心功能初始化完成');
            } catch (error) {
                console.error('应用初始化失败:', error);
                showNotification('应用初始化失败: ' + error.message, 'error');
            }
        }

        // 初始化应用
        function initializeApp() {
            console.log('开始应用初始化...');

            // 首先检查Chart.js
            if (checkChartJS()) {
                // Chart.js可用，直接初始化
                initializeAppCore();
            }
            // 如果Chart.js不可用，checkChartJS会处理重新加载
        }

        // 调整可视化区域高度以匹配左侧控制面板
        function adjustVisualizationHeight() {
            // 暂时禁用自动高度调整，避免无限放大问题
            return;
        }

        // 同步控制面板高度与可视化区域
        function syncControlPanelHeight() {
            const controlPanel = document.querySelector('.control-panel');
            const visualizationArea = document.querySelector('.visualization-area');

            if (controlPanel && visualizationArea) {
                // 等待DOM更新完成
                setTimeout(() => {
                    // 获取可视化区域的实际高度
                    const visualizationHeight = visualizationArea.offsetHeight;

                    // 设置控制面板的最大高度为可视化区域的高度
                    controlPanel.style.maxHeight = visualizationHeight + 'px';

                    // 不设置固定高度，让内容自然流动
                    controlPanel.style.height = 'auto';

                    console.log('控制面板高度已同步:', visualizationHeight + 'px');
                }, 50);
            }
        }

        // 初始化用户信息
        function initUserInfo() {
            // 检查是否有登录用户信息
            let currentUser = localStorage.getItem('currentUser');
            const loginTime = localStorage.getItem('loginTime');

            if (currentUser) {
                // 显示用户名
                const userNameElement = document.getElementById('currentUserName');
                if (userNameElement) {
                    userNameElement.textContent = currentUser;
                }

                console.log('用户已登录:', currentUser, '登录时间:', loginTime);
            } else {
                // 如果没有登录信息，设置默认用户（允许直接访问）
                currentUser = '访客用户';
                const userNameElement = document.getElementById('currentUserName');
                if (userNameElement) {
                    userNameElement.textContent = currentUser;
                }

                console.log('未检测到登录信息，设置为访客用户');
            }

            // 绑定退出登录按钮事件
            const logoutBtn = document.getElementById('logoutBtn');
            if (logoutBtn) {
                logoutBtn.addEventListener('click', function() {
                    // 确认退出
                    if (confirm('确定要退出登录吗？')) {
                        // 清除登录信息
                        localStorage.removeItem('currentUser');
                        localStorage.removeItem('loginTime');

                        // 跳转到登录页面
                        window.location.href = 'index.html';
                    }
                });
            }
        }

        // 重新调整所有图表大小
        function resizeAllCharts() {
            // 如果有图表实例，重新调整它们的大小
            if (typeof charts !== 'undefined' && charts) {
                Object.values(charts).forEach(chart => {
                    if (chart && chart.resize) {
                        // 先更新图表配置以确保正确的尺寸
                        chart.resize();
                        // 强制重新绘制
                        chart.update('none'); // 'none'表示不使用动画
                    }
                });
            }
        }

        // 监听统计分析卡片的显示状态变化
        function observeStatisticsCard() {
            const statisticsCard = document.getElementById('statisticsCard');
            if (statisticsCard) {
                const debouncedAdjust = debounce(adjustVisualizationHeight, 200);

                const observer = new MutationObserver((mutations) => {
                    mutations.forEach((mutation) => {
                        if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
                            // 使用防抖调整高度
                            debouncedAdjust();
                        }
                    });
                });

                observer.observe(statisticsCard, {
                    attributes: true,
                    attributeFilter: ['style']
                });
            }
        }

        // 初始化粒子效果
        function initParticles() {
            try {
                console.log('初始化粒子效果...');
                const particlesContainer = document.getElementById('particles');

                if (!particlesContainer) {
                    console.warn('粒子容器未找到，跳过粒子效果初始化');
                    return;
                }

                const particleCount = 50;

                for (let i = 0; i < particleCount; i++) {
                    const particle = document.createElement('div');
                    particle.className = 'particle';
                    particle.style.left = Math.random() * 100 + '%';
                    particle.style.animationDelay = Math.random() * 15 + 's';
                    particle.style.animationDuration = (Math.random() * 10 + 10) + 's';
                    particlesContainer.appendChild(particle);
                }

                console.log('✓ 粒子效果初始化完成');
            } catch (error) {
                console.error('粒子效果初始化失败:', error);
            }
        }

        // 初始化图表交互
        function initChartInteractions() {
            try {
                console.log('初始化图表交互...');
                const chartCards = document.querySelectorAll('.chart-card');

                if (chartCards.length === 0) {
                    console.warn('未找到图表卡片，跳过交互初始化');
                    return;
                }

            chartCards.forEach(card => {
                // 添加拖拽功能
                let isDragging = false;
                let startX = 0;
                let startY = 0;

                card.addEventListener('mousedown', (e) => {
                    if (e.target.tagName === 'CANVAS') {
                        isDragging = true;
                        startX = e.clientX;
                        startY = e.clientY;
                        card.classList.add('dragging');
                        card.style.cursor = 'grabbing';
                    }
                });

                document.addEventListener('mousemove', (e) => {
                    if (isDragging) {
                        const deltaX = e.clientX - startX;
                        const deltaY = e.clientY - startY;

                        // 实现图表平移
                        panChart(card.id, deltaX, deltaY);

                        startX = e.clientX;
                        startY = e.clientY;
                    }
                });

                document.addEventListener('mouseup', () => {
                    if (isDragging) {
                        isDragging = false;
                        card.classList.remove('dragging');
                        card.style.cursor = 'default';
                    }
                });

                // 添加双击auto功能
                card.addEventListener('dblclick', () => {
                    autoScaleChart(card.id);
                });

                // 添加悬停效果
                card.addEventListener('mouseenter', () => {
                    card.style.transform = 'translateY(-6px) scale(1.02)';
                });

                card.addEventListener('mouseleave', () => {
                    if (!isDragging) {
                        card.style.transform = '';
                    }
                });
            });

            console.log('✓ 图表交互初始化完成');
            } catch (error) {
                console.error('图表交互初始化失败:', error);
            }
        }

        // 图表平移功能
        function panChart(chartId, deltaX, deltaY) {
            const chartType = chartId.replace('Chart', '');
            const chart = charts[chartType + 'Chart'];

            if (chart && chart.options.plugins && chart.options.plugins.zoom) {
                const panSpeed = 0.01;
                chart.pan({
                    x: deltaX * panSpeed,
                    y: -deltaY * panSpeed
                });
            }
        }

        // 示波器Auto功能 - 自动缩放到最佳显示
        function autoScaleChart(chartId) {
            const chartType = chartId.replace('Chart', '');
            const chart = charts[chartType + 'Chart'];

            if (!chart) return;

            // 添加视觉反馈
            const card = document.getElementById(chartId);
            card.style.transform = 'scale(1.1)';
            card.style.transition = 'transform 0.3s ease';

            setTimeout(() => {
                card.style.transform = '';
            }, 300);

            // 根据图表类型获取相应的数据
            let dataToAnalyze;
            if (chartType === 'frequency') {
                // 对于频谱图，使用FFT数据
                dataToAnalyze = computeFFT(currentData);
            } else if (chartType === 'scatter') {
                // 对于散点图，使用散点数据
                dataToAnalyze = prepareScatterData(currentData);
            } else if (chartType === 'bar') {
                // 对于柱状图，使用直方图数据
                const histogram = computeHistogram(currentData);
                dataToAnalyze = histogram.values.map((val, idx) => ({
                    x: parseFloat(histogram.labels[idx]),
                    y: val
                }));
            } else {
                // 其他图表使用原始数据
                dataToAnalyze = currentData;
            }

            if (!dataToAnalyze || dataToAnalyze.length === 0) return;

            // 计算有效数据范围（排除异常值）
            const xValues = dataToAnalyze.map(d => d.x).filter(x => !isNaN(x) && isFinite(x));
            const yValues = dataToAnalyze.map(d => d.y).filter(y => !isNaN(y) && isFinite(y));

            if (xValues.length === 0 || yValues.length === 0) return;

            // 排序并去除极端异常值（前后5%）
            const sortedX = [...xValues].sort((a, b) => a - b);
            const sortedY = [...yValues].sort((a, b) => a - b);

            const xStart = Math.floor(sortedX.length * 0.05);
            const xEnd = Math.ceil(sortedX.length * 0.95);
            const yStart = Math.floor(sortedY.length * 0.05);
            const yEnd = Math.ceil(sortedY.length * 0.95);

            const xMin = sortedX[xStart];
            const xMax = sortedX[xEnd - 1];
            const yMin = sortedY[yStart];
            const yMax = sortedY[yEnd - 1];

            // 计算智能边距
            const xRange = xMax - xMin;
            const yRange = yMax - yMin;
            const xMargin = xRange > 0 ? xRange * 0.1 : Math.abs(xMin) * 0.1 || 1;
            const yMargin = yRange > 0 ? yRange * 0.1 : Math.abs(yMin) * 0.1 || 1;

            // 重置缩放并设置最佳范围
            if (chart.resetZoom) {
                chart.resetZoom();
            }

            // 动态设置坐标轴范围
            if (chart.options.scales) {
                if (chart.options.scales.x) {
                    chart.options.scales.x.min = xMin - xMargin;
                    chart.options.scales.x.max = xMax + xMargin;
                }
                if (chart.options.scales.y) {
                    chart.options.scales.y.min = yMin - yMargin;
                    chart.options.scales.y.max = yMax + yMargin;
                }

                chart.update('active');
            }

            // 显示Auto提示
            showNotification(`${getChartName(chartType)}已自动调整到最佳显示范围`, 'success');
        }

        // 获取图表名称
        function getChartName(chartType) {
            const names = {
                'time': '时域波形图',
                'frequency': '频域分析图',
                'bar': '数据分布图',
                'envelope': '包络线图',
                'psd': '功率谱密度图',
                'phase': '瞬时相位图',
                'operation': '信号运算图',
                'correlation': '相关分析图',
                'cdf': '累积分布函数图',
                'wavelet': '小波变换图',
                'polar': '极坐标图',
                'vector': '矢量图'
            };
            return names[chartType] || '图表';
        }

        // 初始化导航功能
        function initNavigation() {
            console.log('初始化导航功能...');

            // 分类展开/收起
            const categoryHeaders = document.querySelectorAll('.category-header');
            console.log(`找到 ${categoryHeaders.length} 个分类标题`);

            categoryHeaders.forEach((header, index) => {
                console.log(`绑定第 ${index + 1} 个分类标题的点击事件`);

                header.addEventListener('click', (e) => {
                    console.log('分类标题被点击:', header.querySelector('.category-title').textContent);

                    const category = header.closest('.nav-category');
                    const isExpanded = category.classList.contains('expanded');

                    console.log('当前展开状态:', isExpanded);

                    // 收起所有其他分类
                    document.querySelectorAll('.nav-category').forEach(cat => {
                        if (cat !== category) {
                            cat.classList.remove('expanded');
                            const catHeader = cat.querySelector('.category-header');
                            if (catHeader) {
                                catHeader.classList.remove('expanded');
                            }
                        }
                    });

                    // 切换当前分类
                    category.classList.toggle('expanded', !isExpanded);
                    header.classList.toggle('expanded', !isExpanded);

                    console.log('新的展开状态:', !isExpanded);
                });
            });

            // 导航链接点击事件
            document.querySelectorAll('.nav-link').forEach(link => {
                link.addEventListener('click', (e) => {
                    // 如果链接有data-page属性，说明是页面跳转链接，不阻止默认行为
                    if (link.hasAttribute('data-page')) {
                        console.log('页面跳转链接被点击:', link.getAttribute('href'));
                        // 直接跳转，不阻止默认行为
                        return;
                    }

                    // 如果链接有data-function属性，处理功能调用
                    if (link.hasAttribute('data-function')) {
                        e.preventDefault();
                        const functionType = link.dataset.function;
                        handleNavFunction(functionType);

                        // 更新活动状态
                        document.querySelectorAll('.nav-link').forEach(l => l.classList.remove('active'));
                        link.classList.add('active');
                    }
                });
            });
        }

        // 处理导航功能
        function handleNavFunction(functionType) {
            if (!currentData) {
                showNotification('请先导入或加载数据', 'warning');
                return;
            }

            switch (functionType) {

                case 'denoise':
                    denoise();
                    break;
                case 'spectrum':
                    showNotification('频谱分析已在频域图表中显示', 'info');
                    break;

            }
        }

        // 初始化数据导入
        function initDataImport() {
            const fileImport = document.getElementById('fileImport');
            const fileInput = document.getElementById('fileInput');
            const historyData = document.getElementById('historyData');
            const compareFilesBtn = document.getElementById('compareFilesBtn');
            const clearFilesBtn = document.getElementById('clearFilesBtn');

            fileImport.addEventListener('click', () => {
                fileInput.click();
            });

            fileInput.addEventListener('change', (e) => {
                const files = Array.from(e.target.files);
                if (files.length > 0) {
                    handleMultipleFileImport(files);
                }
                // 清空input，允许重复选择相同文件
                e.target.value = '';
            });

            historyData.addEventListener('click', () => {
                showHistoryPanel();
            });

            compareFilesBtn.addEventListener('click', () => {
                compareMultipleDataSets();
            });

            clearFilesBtn.addEventListener('click', () => {
                clearUploadedFiles();
            });
        }

        // 处理多文件导入
        async function handleMultipleFileImport(files) {
            if (files.length > 3) {
                showNotification('最多只能同时上传3个文件', 'warning');
                files = files.slice(0, 3);
            }

            try {
                showNotification(`正在导入${files.length}个文件...`, 'info');

                for (let i = 0; i < files.length; i++) {
                    const file = files[i];

                    // 检查是否已经上传了相同名称的文件
                    if (multipleDataSets.find(ds => ds.fileName === file.name)) {
                        showNotification(`文件 ${file.name} 已存在，跳过导入`, 'warning');
                        continue;
                    }

                    // 检查是否超过3个文件
                    if (multipleDataSets.length >= 3) {
                        showNotification('已达到最大文件数量限制(3个)', 'warning');
                        break;
                    }

                    let data;
                    if (file.name.endsWith('.xlsx') || file.name.endsWith('.xls')) {
                        data = await parseExcel(file);
                    } else if (file.name.endsWith('.json')) {
                        const text = await file.text();
                        data = JSON.parse(text);
                    } else if (file.name.endsWith('.csv')) {
                        const text = await file.text();
                        data = parseCSV(text);
                    } else {
                        const text = await file.text();
                        data = parseTXT(text);
                    }

                    // 添加到多数据集数组
                    const dataSetIndex = multipleDataSets.length;
                    multipleDataSets.push({
                        data: data,
                        fileName: file.name,
                        color: dataSetColors[dataSetIndex],
                        timestamp: Date.now()
                    });

                    addToHistory(data, file.name, 'file');
                }

                updateFilesList();
                showNotification(`成功导入${multipleDataSets.length}个文件`, 'success');

                // 如果只有一个文件，直接显示
                if (multipleDataSets.length === 1) {
                    loadData(multipleDataSets[0].data, `导入文件: ${multipleDataSets[0].fileName}`);
                }

            } catch (error) {
                console.error('文件导入失败:', error);
                showNotification('文件导入失败，请检查文件格式', 'error');
            }
        }

        // 处理单文件导入（保持向后兼容）
        async function handleFileImport(file) {
            await handleMultipleFileImport([file]);
        }

        // 解析CSV文件
        function parseCSV(text) {
            const lines = text.trim().split('\n');
            const data = [];

            lines.forEach((line, index) => {
                if (index === 0) return; // 跳过标题行
                const values = line.split(',');
                if (values.length >= 2) {
                    data.push({
                        x: parseFloat(values[0]) || index * 0.01, // 增加时间间隔
                        y: parseFloat(values[1]) || 0
                    });
                } else if (values.length >= 1) {
                    const value = parseFloat(values[0]);
                    if (!isNaN(value)) {
                        data.push({
                            x: index * 0.01, // 10ms间隔，更大的时间范围
                            y: value
                        });
                    }
                }
            });

            return data;
        }

        // 解析TXT文件
        function parseTXT(text) {
            const lines = text.trim().split('\n');
            const data = [];

            lines.forEach((line, index) => {
                const value = parseFloat(line.trim());
                if (!isNaN(value)) {
                    data.push({
                        x: index * 0.01, // 10ms间隔，更大的时间范围
                        y: value
                    });
                }
            });

            return data;
        }

        // 解析Excel文件
        async function parseExcel(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = function(e) {
                    try {
                        const data = new Uint8Array(e.target.result);
                        const workbook = XLSX.read(data, { type: 'array' });

                        // 获取第一个工作表
                        const firstSheetName = workbook.SheetNames[0];
                        const worksheet = workbook.Sheets[firstSheetName];

                        // 转换为JSON
                        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

                        const result = [];
                        jsonData.forEach((row, index) => {
                            if (index === 0) return; // 跳过标题行
                            if (row.length >= 2) {
                                const x = parseFloat(row[0]);
                                const y = parseFloat(row[1]);
                                if (!isNaN(x) && !isNaN(y)) {
                                    result.push({ x: x, y: y });
                                }
                            } else if (row.length >= 1) {
                                const y = parseFloat(row[0]);
                                if (!isNaN(y)) {
                                    result.push({ x: index * 0.01, y: y }); // 10ms间隔
                                }
                            }
                        });

                        resolve(result);
                    } catch (error) {
                        reject(error);
                    }
                };
                reader.onerror = function() {
                    reject(new Error('文件读取失败'));
                };
                reader.readAsArrayBuffer(file);
            });
        }

        // 初始化噪声配置
        function initNoiseConfig() {
            const noiseCheck = document.getElementById('noiseCheck');
            const noiseConfigPanel = document.getElementById('noiseConfigPanel');
            const noiseAmpSlider = document.getElementById('noiseAmpSlider');

            // 噪声配置面板控制
            noiseCheck.addEventListener('change', (e) => {
                if (e.target.checked) {
                    noiseConfigPanel.style.display = 'block';
                    noiseConfigPanel.classList.add('show');
                    // 默认选择高斯噪声
                    document.getElementById('gaussianNoise').checked = true;
                } else {
                    noiseConfigPanel.style.display = 'none';
                    noiseConfigPanel.classList.remove('show');
                    // 清除所有噪声选择
                    document.querySelectorAll('.noise-checkbox').forEach(cb => cb.checked = false);
                }
            });

            // 噪声幅度滑动条
            noiseAmpSlider.addEventListener('input', (e) => {
                document.getElementById('noiseAmpValue').textContent = e.target.value;
            });

            // 初始化噪声幅度显示
            noiseAmpSlider.dispatchEvent(new Event('input'));
        }

        // 初始化示例数据
        function initSampleData() {
            const sampleItems = document.querySelectorAll('.sample-item');
            const freqSlider = document.getElementById('freqSlider');
            const ampSlider = document.getElementById('ampSlider');
            const pointsSlider = document.getElementById('pointsSlider');
            const sampleRateSlider = document.getElementById('sampleRateSlider');
            const globalSampleRateSlider = document.getElementById('globalSampleRateSlider');
            const loadSampleBtn = document.getElementById('loadSampleBtn');
            const exportExcelBtn = document.getElementById('exportExcelBtn');

            // 示例数据选择
            sampleItems.forEach(item => {
                item.addEventListener('click', () => {
                    sampleItems.forEach(i => i.classList.remove('active'));
                    item.classList.add('active');
                    currentWaveType = item.dataset.type;
                });
            });

            // 参数控制
            freqSlider.addEventListener('input', (e) => {
                const logValue = parseFloat(e.target.value);
                const freq = Math.pow(10, logValue);
                let freqText;
                if (freq >= 1000000) {
                    freqText = (freq / 1000000).toFixed(1) + 'MHz';
                } else if (freq >= 1000) {
                    freqText = (freq / 1000).toFixed(1) + 'kHz';
                } else {
                    freqText = freq.toFixed(1) + 'Hz';
                }
                document.getElementById('freqValue').textContent = freqText;
            });

            ampSlider.addEventListener('input', (e) => {
                document.getElementById('ampValue').textContent = e.target.value;
            });

            pointsSlider.addEventListener('input', (e) => {
                document.getElementById('pointsValue').textContent = e.target.value;
            });



            // 全局采样频率滑动条
            globalSampleRateSlider.addEventListener('input', (e) => {
                const logValue = parseFloat(e.target.value);
                const sampleRate = Math.pow(10, logValue);
                let rateText;
                if (sampleRate >= 1000000) {
                    rateText = (sampleRate / 1000000).toFixed(1) + 'MHz';
                } else if (sampleRate >= 1000) {
                    rateText = (sampleRate / 1000).toFixed(1) + 'kHz';
                } else {
                    rateText = sampleRate.toFixed(1) + 'Hz';
                }
                document.getElementById('globalSampleRateValue').textContent = rateText;

                // 同步到示例数据的采样频率（如果存在）
                if (sampleRateSlider) {
                    sampleRateSlider.value = e.target.value;
                    document.getElementById('sampleRateValue').textContent = rateText;
                }

                // 重新计算和更新与采样频率相关的图表
                if (currentData && currentData.length > 0) {
                    // 更新频谱图
                    updateFrequencyChart(currentData);
                    // 更新功率谱密度图
                    updatePSDChart(currentData);
                    // 更新瞬时相位图
                    updatePhaseChart(currentData);
                    // 显示通知
                    showNotification(`采样频率已更新为 ${rateText}，相关图表已重新计算`, 'success');
                }
            });

            // 采样频率滑动条（示例数据专用，已被全局控制器替代）
            if (sampleRateSlider) {
                sampleRateSlider.addEventListener('input', (e) => {
                    const logValue = parseFloat(e.target.value);
                    const sampleRate = Math.pow(10, logValue);
                    let rateText;
                    if (sampleRate >= 1000000) {
                        rateText = (sampleRate / 1000000).toFixed(1) + 'MHz';
                    } else if (sampleRate >= 1000) {
                        rateText = (sampleRate / 1000).toFixed(1) + 'kHz';
                    } else {
                        rateText = sampleRate.toFixed(1) + 'Hz';
                    }
                    document.getElementById('sampleRateValue').textContent = rateText;

                    // 同步到全局采样频率
                    if (globalSampleRateSlider) {
                        globalSampleRateSlider.value = e.target.value;
                        document.getElementById('globalSampleRateValue').textContent = rateText;
                    }

                    // 重新计算和更新与采样频率相关的图表
                    if (currentData && currentData.length > 0) {
                        updateFrequencyChart(currentData);
                        updatePSDChart(currentData);
                        updatePhaseChart(currentData);
                        showNotification(`采样频率已更新为 ${rateText}，相关图表已重新计算`, 'success');
                    }
                });
            }

            // 加载示例数据
            loadSampleBtn.addEventListener('click', () => {
                const logFreq = parseFloat(freqSlider.value);
                const freq = Math.pow(10, logFreq);
                const amp = parseFloat(ampSlider.value);
                const points = parseInt(pointsSlider.value);

                const data = generateWaveform(currentWaveType, freq, amp, false, points, [], 0.2);
                let freqText;
                if (freq >= 1000000) {
                    freqText = (freq / 1000000).toFixed(1) + 'MHz';
                } else if (freq >= 1000) {
                    freqText = (freq / 1000).toFixed(1) + 'kHz';
                } else {
                    freqText = freq.toFixed(1) + 'Hz';
                }

                const title = `${getWaveformName(currentWaveType)} (${freqText}, ${amp}倍, ${points}点)`;

                loadData(data, title);
                addToHistory(data, title, 'sample');
                showNotification('示例数据加载成功', 'success');
            });

            // Excel导出
            exportExcelBtn.addEventListener('click', () => {
                exportToExcel();
            });

            // 初始化频率和采样频率显示
            if (freqSlider) {
                freqSlider.dispatchEvent(new Event('input'));
            }
            if (sampleRateSlider) {
                sampleRateSlider.dispatchEvent(new Event('input'));
            }
            // 初始化全局采样频率显示
            if (globalSampleRateSlider) {
                globalSampleRateSlider.dispatchEvent(new Event('input'));
            }
        }

        // 生成波形数据
        function generateWaveform(type, frequency = 1000, amplitude = 1, addNoise = false, numPoints = 512, noiseTypes = [], noiseAmplitude = 0.2) {
            const data = [];

            // 获取采样频率设置
            let sampleRate = 2000; // 默认2kHz采样频率
            const globalSampleRateSlider = document.getElementById('globalSampleRateSlider');
            const sampleRateSlider = document.getElementById('sampleRateSlider');

            if (globalSampleRateSlider) {
                const logValue = parseFloat(globalSampleRateSlider.value);
                sampleRate = Math.pow(10, logValue);
            } else if (sampleRateSlider) {
                const logValue = parseFloat(sampleRateSlider.value);
                sampleRate = Math.pow(10, logValue);
            }

            // 确保采样频率至少是信号频率的2倍（奈奎斯特定理）
            sampleRate = Math.max(sampleRate, frequency * 2.5);

            // 计算时间步长
            const timeStep = 1 / sampleRate;

            // 计算显示时长：基于数据点数和采样频率
            const duration = (numPoints - 1) * timeStep;

            for (let i = 0; i < numPoints; i++) {
                const t = i * timeStep;
                const omega = 2 * Math.PI * frequency;
                let y;

                switch (type) {
                    case 'sine':
                        y = amplitude * Math.sin(omega * t);
                        break;
                    case 'cosine':
                        y = amplitude * Math.cos(omega * t);
                        break;
                    case 'square':
                        y = amplitude * Math.sign(Math.sin(omega * t));
                        break;
                    case 'triangle':
                        y = amplitude * (2 / Math.PI) * Math.asin(Math.sin(omega * t));
                        break;
                    case 'sawtooth':
                        y = amplitude * 2 * (frequency * t - Math.floor(frequency * t + 0.5));
                        break;
                    case 'pulse':
                        y = amplitude * (Math.sin(omega * t) > 0.5 ? 1 : -1);
                        break;
                    default:
                        y = amplitude * Math.sin(omega * t);
                }

                // 添加多种噪声
                if (addNoise && noiseTypes.length > 0) {
                    let noiseValue = 0;

                    noiseTypes.forEach(noiseType => {
                        switch (noiseType) {
                            case 'gaussian':
                                // 高斯噪声 (Box-Muller变换)
                                const u1 = Math.random();
                                const u2 = Math.random();
                                const gaussian = Math.sqrt(-2 * Math.log(u1)) * Math.cos(2 * Math.PI * u2);
                                noiseValue += gaussian * noiseAmplitude * amplitude * 0.3;
                                break;

                            case 'impulse':
                                // 脉冲噪声 (椒盐噪声)
                                if (Math.random() < 0.05) { // 5%概率出现脉冲
                                    noiseValue += (Math.random() > 0.5 ? 1 : -1) * noiseAmplitude * amplitude * 2;
                                }
                                break;

                            case 'white':
                                // 白噪声 (均匀分布)
                                noiseValue += (Math.random() - 0.5) * noiseAmplitude * amplitude;
                                break;

                            case 'flicker':
                                // 闪烁噪声 (1/f噪声)
                                const flickerFreq = 1 / (i + 1);
                                noiseValue += Math.sin(2 * Math.PI * flickerFreq * t) * noiseAmplitude * amplitude * 0.5;
                                break;

                            case 'thermal':
                                // 热噪声 (约翰逊噪声)
                                const thermal = Math.sqrt(-2 * Math.log(Math.random())) * Math.cos(2 * Math.PI * Math.random());
                                noiseValue += thermal * noiseAmplitude * amplitude * 0.1;
                                break;

                            case 'colored':
                                // 色噪声 (粉红噪声近似)
                                const coloredFreq = frequency * (0.1 + 0.9 * Math.random());
                                noiseValue += Math.sin(2 * Math.PI * coloredFreq * t) * noiseAmplitude * amplitude * 0.4;
                                break;
                        }
                    });

                    y += noiseValue;
                }

                data.push({ x: t, y: y });
            }

            return data;
        }

        // 获取波形名称
        function getWaveformName(type) {
            const names = {
                sine: '正弦波',
                cosine: '余弦波',
                square: '方波',
                triangle: '三角波',
                sawtooth: '锯齿波',
                pulse: '脉冲波'
            };
            return names[type] || '未知波形';
        }

        // 加载默认数据
        function loadDefaultData() {
            console.log('Loading default data...');

            // 立即检查函数是否存在
            if (typeof generateWaveform !== 'function') {
                console.error('generateWaveform function not found!');
                return;
            }

            setTimeout(() => {
                try {
                    // 生成默认正弦波数据
                    const defaultData = generateWaveform('sine', 1000, 1, false, 512, [], 0.2);
                    console.log('Generated default data:', defaultData.length, 'points');

                    if (defaultData && defaultData.length > 0) {
                        // 加载数据并更新所有图表
                        loadData(defaultData, '默认正弦波 (1kHz, 512点)');
                        console.log('Default data loaded successfully');
                        showNotification('已加载默认示例数据 - 正弦波', 'success');
                    } else {
                        console.error('Failed to generate default data');
                        showNotification('默认数据生成失败', 'error');
                    }

                    // 强制重新渲染图表
                    setTimeout(() => {
                        Object.keys(charts).forEach(chartKey => {
                            if (charts[chartKey] && charts[chartKey].resize) {
                                charts[chartKey].resize();
                            }
                        });
                    }, 300);
                } catch (error) {
                    console.error('Error loading default data:', error);
                    showNotification('加载默认数据时出错', 'error');
                }
            }, 500); // 减少延迟时间
        }

        // 更新文件列表显示
        function updateFilesList() {
            const uploadedFilesList = document.getElementById('uploadedFilesList');
            const fileListContainer = document.getElementById('fileListContainer');

            if (multipleDataSets.length === 0) {
                uploadedFilesList.style.display = 'none';
                return;
            }

            uploadedFilesList.style.display = 'block';
            fileListContainer.innerHTML = '';

            multipleDataSets.forEach((dataSet, index) => {
                const fileItem = document.createElement('div');
                fileItem.className = `file-item dataset-${index + 1}`;
                fileItem.innerHTML = `
                    <div class="file-item-info">
                        <div class="file-item-color" style="background-color: ${dataSet.color.border};"></div>
                        <span class="file-item-name" title="${dataSet.fileName}">${dataSet.fileName}</span>
                    </div>
                    <button class="file-item-remove" onclick="removeDataSet(${index})" title="移除文件">
                        <i class="fas fa-times"></i>
                    </button>
                `;
                fileListContainer.appendChild(fileItem);
            });
        }

        // 移除数据集
        function removeDataSet(index) {
            if (index >= 0 && index < multipleDataSets.length) {
                const fileName = multipleDataSets[index].fileName;
                multipleDataSets.splice(index, 1);

                // 重新分配颜色
                multipleDataSets.forEach((dataSet, newIndex) => {
                    dataSet.color = dataSetColors[newIndex];
                });

                updateFilesList();
                showNotification(`已移除文件: ${fileName}`, 'success');

                // 如果还有数据，重新对比显示
                if (multipleDataSets.length > 0) {
                    compareMultipleDataSets();
                } else {
                    // 如果没有数据了，加载默认数据
                    loadDefaultData();
                }
            }
        }

        // 清空所有上传的文件
        function clearUploadedFiles() {
            multipleDataSets = [];
            updateFilesList();
            loadDefaultData();
            showNotification('已清空所有文件', 'success');
        }

        // 对比多个数据集
        function compareMultipleDataSets() {
            if (multipleDataSets.length === 0) {
                showNotification('请先上传文件', 'warning');
                return;
            }

            // 使用第一个数据集作为主数据
            currentData = multipleDataSets[0].data;

            // 更新所有图表以支持多数据集对比
            updateAllChartsWithMultipleDataSets();
            updateStatistics(currentData);

            // 显示统计分析卡片
            const statisticsCard = document.getElementById('statisticsCard');
            statisticsCard.style.display = 'block';

            // 延迟调整高度以确保DOM更新完成，并且图表已经渲染
            setTimeout(() => {
                adjustVisualizationHeight();
            }, 300);

            showNotification(`正在对比显示${multipleDataSets.length}个数据集`, 'success');
        }

        // 加载数据并更新图表
        function loadData(data, title) {
            currentData = data;
            updateAllCharts(data, title);
            updateStatistics(data);

            // 显示统计分析卡片
            const statisticsCard = document.getElementById('statisticsCard');
            statisticsCard.style.display = 'block';

            // 延迟调整高度以确保DOM更新完成，并且图表已经渲染
            setTimeout(() => {
                adjustVisualizationHeight();
                syncControlPanelHeight();
            }, 300);
        }

        // 更新统计分析
        function updateStatistics(data) {
            const values = data.map(d => d.y);
            const n = values.length;

            // 基础统计
            const mean = values.reduce((sum, val) => sum + val, 0) / n;
            const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / n;
            const stdDev = Math.sqrt(variance);
            const max = Math.max(...values);
            const min = Math.min(...values);
            const peakToPeak = max - min;
            const rms = Math.sqrt(values.reduce((sum, val) => sum + val * val, 0) / n);

            // 更新显示
            document.getElementById('statCount').textContent = n;
            document.getElementById('statMean').textContent = mean.toFixed(3);
            document.getElementById('statVariance').textContent = variance.toFixed(3);
            document.getElementById('statStdDev').textContent = stdDev.toFixed(3);
            document.getElementById('statMax').textContent = max.toFixed(3);
            document.getElementById('statMin').textContent = min.toFixed(3);
            document.getElementById('statPeakToPeak').textContent = peakToPeak.toFixed(3);
            document.getElementById('statRMS').textContent = rms.toFixed(3);
        }

        // Excel导出功能
        function exportToExcel() {
            if (!currentData) {
                showNotification('请先加载数据', 'warning');
                return;
            }

            // 创建CSV内容
            let csvContent = "时间(s),幅度\n";
            currentData.forEach(point => {
                csvContent += `${point.x.toFixed(8)},${point.y.toFixed(8)}\n`;
            });

            // 创建Blob并下载
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `waveform_data_${new Date().getTime()}.csv`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            showNotification('波形数据已导出为CSV文件', 'success');
        }

        // 更新所有图表（支持多数据集）
        function updateAllChartsWithMultipleDataSets() {
            updateTimeChartWithMultipleDataSets();
            updateFrequencyChartWithMultipleDataSets();
            updateBarChartWithMultipleDataSets();
            updateEnvelopeChartWithMultipleDataSets();
            updatePSDChartWithMultipleDataSets();
            updatePhaseChartWithMultipleDataSets();
            updateOperationChart();
            updateCorrelationChart();
            updateCDFChart(multipleDataSets[0].data);
            updateWaveletChart(multipleDataSets[0].data);
            updatePolarChart(multipleDataSets[0].data);
            updateVectorChart(multipleDataSets[0].data);
        }

        // 更新所有图表（单数据集）
        function updateAllCharts(data, title) {
            updateTimeChart(data, title);
            updateFrequencyChart(data);
            updateBarChart(data);
            updateEnvelopeChart(data);
            updatePSDChart(data);
            updatePhaseChart(data);
            updateCDFChart(data);
            updateWaveletChart(data);
            updatePolarChart(data);
            updateVectorChart(data);
        }

        // 更新时域波形图（多数据集）
        function updateTimeChartWithMultipleDataSets() {
            const canvas = document.getElementById('timeChartCanvas');
            const ctx = canvas.getContext('2d');

            if (charts.timeChart) {
                charts.timeChart.destroy();
            }

            const datasets = multipleDataSets.map((dataSet, index) => ({
                label: dataSet.fileName,
                data: dataSet.data,
                borderColor: dataSet.color.border,
                backgroundColor: dataSet.color.background,
                borderWidth: 2,
                fill: false,
                tension: 0.4,
                pointRadius: 0,
                pointHoverRadius: 4
            }));

            charts.timeChart = new Chart(ctx, {
                type: 'line',
                data: { datasets },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top',
                            labels: {
                                usePointStyle: true,
                                padding: 15,
                                font: { size: 11 }
                            }
                        }
                    },
                    scales: {
                        x: {
                            type: 'linear',
                            position: 'bottom',
                            title: { display: true, text: '时间 (s)' }
                        },
                        y: {
                            title: { display: true, text: '幅度' }
                        }
                    },
                    animation: {
                        duration: 500,
                        easing: 'easeInOutQuart',
                        resize: { duration: 0 }
                    },
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    },
                    plugins: {
                        zoom: {
                            zoom: {
                                wheel: { enabled: true },
                                pinch: { enabled: true },
                                mode: 'xy',
                            },
                            pan: { enabled: true, mode: 'xy' }
                        }
                    }
                }
            });
        }

        // 更新时域波形图（单数据集）
        function updateTimeChart(data, title) {
            // 检查Chart.js是否可用
            if (typeof Chart === 'undefined') {
                console.error('Chart.js未加载，无法更新时域图表');
                showNotification('图表库未加载，无法显示图表', 'error');
                return;
            }

            const canvas = document.getElementById('timeChartCanvas');
            if (!canvas) {
                console.error('找不到时域图表画布元素');
                return;
            }

            const ctx = canvas.getContext('2d');

            if (charts.timeChart) {
                charts.timeChart.destroy();
            }

            try {
                charts.timeChart = new Chart(ctx, {
                type: 'line',
                data: {
                    datasets: [{
                        label: title,
                        data: data,
                        borderColor: '#3b82f6',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        borderWidth: 2,
                        fill: true,
                        tension: 0.4,
                        pointRadius: 0,
                        pointHoverRadius: 4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { display: false },
                        zoom: {
                            zoom: {
                                wheel: {
                                    enabled: true,
                                },
                                pinch: {
                                    enabled: true
                                },
                                mode: 'xy',
                            },
                            pan: {
                                enabled: true,
                                mode: 'xy',
                            }
                        }
                    },
                    scales: {
                        x: {
                            type: 'linear',
                            position: 'bottom',
                            title: { display: true, text: '时间 (s)' }
                        },
                        y: {
                            title: { display: true, text: '幅度' }
                        }
                    },
                    animation: {
                        duration: 500,
                        easing: 'easeInOutQuart',
                        resize: {
                            duration: 0 // 禁用resize动画，避免过度放大
                        }
                    },
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    }
                }
            });
            } catch (error) {
                console.error('创建时域图表失败:', error);
                showNotification('时域图表创建失败: ' + error.message, 'error');
            }
        }

        // 更新频域分析图（多数据集）
        function updateFrequencyChartWithMultipleDataSets() {
            const canvas = document.getElementById('frequencyChartCanvas');
            const ctx = canvas.getContext('2d');

            if (charts.frequencyChart) {
                charts.frequencyChart.destroy();
            }

            const datasets = multipleDataSets.map((dataSet, index) => {
                const fftData = computeFFT(dataSet.data);
                return {
                    label: `${dataSet.fileName} 频谱`,
                    data: fftData,
                    borderColor: dataSet.color.border,
                    backgroundColor: dataSet.color.background,
                    borderWidth: 2,
                    fill: false,
                    tension: 0.2,
                    pointRadius: 0
                };
            });

            charts.frequencyChart = new Chart(ctx, {
                type: 'line',
                data: { datasets },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top',
                            labels: {
                                usePointStyle: true,
                                padding: 15,
                                font: { size: 11 }
                            }
                        },
                        zoom: {
                            zoom: {
                                wheel: { enabled: true },
                                pinch: { enabled: true },
                                mode: 'xy',
                            },
                            pan: { enabled: true, mode: 'xy' }
                        }
                    },
                    scales: {
                        x: {
                            type: 'linear',
                            title: { display: true, text: '频率 (Hz)' }
                        },
                        y: {
                            title: { display: true, text: '幅度' }
                        }
                    },
                    animation: { duration: 1000, easing: 'easeInOutQuart' },
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    }
                }
            });
        }

        // 更新频域分析图（单数据集）
        function updateFrequencyChart(data) {
            // 检查Chart.js是否可用
            if (typeof Chart === 'undefined') {
                console.error('Chart.js未加载，无法更新频域图表');
                showNotification('图表库未加载，无法显示频域图表', 'error');
                return;
            }

            const fftData = computeFFT(data);
            const canvas = document.getElementById('frequencyChartCanvas');
            if (!canvas) {
                console.error('找不到频域图表画布元素');
                return;
            }

            const ctx = canvas.getContext('2d');

            if (charts.frequencyChart) {
                charts.frequencyChart.destroy();
            }

            try {
                charts.frequencyChart = new Chart(ctx, {
                type: 'line',
                data: {
                    datasets: [{
                        label: '频谱',
                        data: fftData,
                        borderColor: '#0ea5e9',
                        backgroundColor: 'rgba(14, 165, 233, 0.1)',
                        borderWidth: 2,
                        fill: true,
                        tension: 0.2,
                        pointRadius: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { display: false },
                        zoom: {
                            zoom: {
                                wheel: {
                                    enabled: true,
                                },
                                pinch: {
                                    enabled: true
                                },
                                mode: 'xy',
                            },
                            pan: {
                                enabled: true,
                                mode: 'xy',
                            }
                        }
                    },
                    scales: {
                        x: {
                            type: 'linear',
                            title: { display: true, text: '频率 (Hz)' }
                        },
                        y: {
                            title: { display: true, text: '幅度' }
                        }
                    },
                    animation: { duration: 1000, easing: 'easeInOutQuart' },
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    }
                }
            });
            } catch (error) {
                console.error('创建频域图表失败:', error);
                showNotification('频域图表创建失败: ' + error.message, 'error');
            }
        }

        // 更新柱状图（多数据集）
        function updateBarChartWithMultipleDataSets() {
            const canvas = document.getElementById('barChartCanvas');
            const ctx = canvas.getContext('2d');

            if (charts.barChart) {
                charts.barChart.destroy();
            }

            // 计算所有数据集的直方图，使用相同的区间
            const allValues = multipleDataSets.flatMap(ds => ds.data.map(d => d.y));
            const min = Math.min(...allValues);
            const max = Math.max(...allValues);
            const bins = 20;
            const binWidth = (max - min) / bins;

            const labels = [];
            for (let i = 0; i < bins; i++) {
                labels.push((min + i * binWidth).toFixed(2));
            }

            const datasets = multipleDataSets.map((dataSet, index) => {
                const histogram = computeHistogramWithRange(dataSet.data, min, max, bins);
                return {
                    label: dataSet.fileName,
                    data: histogram.values,
                    backgroundColor: dataSet.color.background.replace('0.1', '0.8'),
                    borderColor: dataSet.color.border,
                    borderWidth: 1
                };
            });

            charts.barChart = new Chart(ctx, {
                type: 'bar',
                data: { labels, datasets },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top',
                            labels: {
                                usePointStyle: true,
                                padding: 15,
                                font: { size: 11 }
                            }
                        }
                    },
                    scales: {
                        x: { title: { display: true, text: '数值区间' } },
                        y: { title: { display: true, text: '频次' } }
                    },
                    animation: { duration: 1000, easing: 'easeInOutQuart' }
                }
            });
        }

        // 更新柱状图（单数据集）
        function updateBarChart(data) {
            const histogram = computeHistogram(data);
            const canvas = document.getElementById('barChartCanvas');
            const ctx = canvas.getContext('2d');

            if (charts.barChart) {
                charts.barChart.destroy();
            }

            charts.barChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: histogram.labels,
                    datasets: [{
                        label: '频次',
                        data: histogram.values,
                        backgroundColor: 'rgba(59, 130, 246, 0.8)',
                        borderColor: '#3b82f6',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { legend: { display: false } },
                    scales: {
                        x: { title: { display: true, text: '数值区间' } },
                        y: { title: { display: true, text: '频次' } }
                    },
                    animation: { duration: 1000, easing: 'easeInOutQuart' }
                }
            });
        }

        // 更新包络线图（多数据集）
        function updateEnvelopeChartWithMultipleDataSets() {
            const canvas = document.getElementById('envelopeChartCanvas');
            const ctx = canvas.getContext('2d');

            if (charts.envelopeChart) {
                charts.envelopeChart.destroy();
            }

            const datasets = multipleDataSets.map((dataSet, index) => {
                const envelopeData = computeEnvelope(dataSet.data);
                return {
                    label: `${dataSet.fileName} 包络`,
                    data: envelopeData,
                    borderColor: dataSet.color.border,
                    backgroundColor: dataSet.color.background,
                    borderWidth: 2,
                    fill: false,
                    tension: 0.4,
                    pointRadius: 0
                };
            });

            charts.envelopeChart = new Chart(ctx, {
                type: 'line',
                data: { datasets },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top',
                            labels: {
                                usePointStyle: true,
                                padding: 15,
                                font: { size: 11 }
                            }
                        },
                        zoom: {
                            zoom: {
                                wheel: { enabled: true },
                                pinch: { enabled: true },
                                mode: 'xy',
                            },
                            pan: { enabled: true, mode: 'xy' }
                        }
                    },
                    scales: {
                        x: {
                            type: 'linear',
                            title: { display: true, text: '时间 (s)' }
                        },
                        y: {
                            title: { display: true, text: '幅度' }
                        }
                    },
                    animation: { duration: 1000, easing: 'easeInOutQuart' }
                }
            });
        }

        // 更新包络线图（单数据集）
        function updateEnvelopeChart(data) {
            const envelopeData = computeEnvelope(data);
            const canvas = document.getElementById('envelopeChartCanvas');
            const ctx = canvas.getContext('2d');

            if (charts.envelopeChart) {
                charts.envelopeChart.destroy();
            }

            charts.envelopeChart = new Chart(ctx, {
                type: 'line',
                data: {
                    datasets: [{
                        label: '包络线',
                        data: envelopeData,
                        borderColor: '#10b981',
                        backgroundColor: 'rgba(16, 185, 129, 0.1)',
                        borderWidth: 2,
                        fill: true,
                        tension: 0.4,
                        pointRadius: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { display: false },
                        zoom: {
                            zoom: {
                                wheel: { enabled: true },
                                pinch: { enabled: true },
                                mode: 'xy',
                            },
                            pan: { enabled: true, mode: 'xy' }
                        }
                    },
                    scales: {
                        x: {
                            type: 'linear',
                            title: { display: true, text: '时间 (s)' }
                        },
                        y: {
                            title: { display: true, text: '幅度' },
                            ticks: {
                                callback: function(value) {
                                    return Number(value).toFixed(3);
                                }
                            }
                        }
                    },
                    animation: { duration: 1000, easing: 'easeInOutQuart' }
                }
            });
        }

        // 更新散点图（多数据集）
        function updateScatterChartWithMultipleDataSets() {
            const canvas = document.getElementById('scatterChartCanvas');
            const ctx = canvas.getContext('2d');

            if (charts.scatterChart) {
                charts.scatterChart.destroy();
            }

            const datasets = multipleDataSets.map((dataSet, index) => {
                const scatterData = prepareScatterData(dataSet.data);
                return {
                    label: dataSet.fileName,
                    data: scatterData,
                    backgroundColor: dataSet.color.background.replace('0.1', '0.6'),
                    borderColor: dataSet.color.border,
                    borderWidth: 1,
                    pointRadius: 3
                };
            });

            charts.scatterChart = new Chart(ctx, {
                type: 'scatter',
                data: { datasets },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top',
                            labels: {
                                usePointStyle: true,
                                padding: 15,
                                font: { size: 11 }
                            }
                        }
                    },
                    scales: {
                        x: { title: { display: true, text: 'X值' } },
                        y: { title: { display: true, text: 'Y值' } }
                    },
                    animation: { duration: 1000, easing: 'easeInOutQuart' },
                    plugins: {
                        zoom: {
                            zoom: {
                                wheel: { enabled: true },
                                pinch: { enabled: true },
                                mode: 'xy',
                            },
                            pan: { enabled: true, mode: 'xy' }
                        }
                    }
                }
            });
        }

        // 更新散点图（单数据集）
        function updateScatterChart(data) {
            const scatterData = prepareScatterData(data);
            const canvas = document.getElementById('scatterChartCanvas');
            const ctx = canvas.getContext('2d');

            if (charts.scatterChart) {
                charts.scatterChart.destroy();
            }

            charts.scatterChart = new Chart(ctx, {
                type: 'scatter',
                data: {
                    datasets: [{
                        label: '数据点',
                        data: scatterData,
                        backgroundColor: 'rgba(14, 165, 233, 0.6)',
                        borderColor: '#0ea5e9',
                        borderWidth: 1,
                        pointRadius: 3
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { legend: { display: false } },
                    scales: {
                        x: { title: { display: true, text: 'X值' } },
                        y: { title: { display: true, text: 'Y值' } }
                    },
                    animation: { duration: 1000, easing: 'easeInOutQuart' },
                    plugins: {
                        zoom: {
                            zoom: {
                                wheel: { enabled: true },
                                pinch: { enabled: true },
                                mode: 'xy',
                            },
                            pan: { enabled: true, mode: 'xy' }
                        }
                    }
                }
            });
        }

        // 更新功率谱密度图（多数据集）
        function updatePSDChartWithMultipleDataSets() {
            const canvas = document.getElementById('psdChartCanvas');
            const ctx = canvas.getContext('2d');

            if (charts.psdChart) {
                charts.psdChart.destroy();
            }

            const datasets = multipleDataSets.map((dataSet, index) => {
                const psdData = computePSD(dataSet.data);
                return {
                    label: `${dataSet.fileName} PSD`,
                    data: psdData,
                    borderColor: dataSet.color.border,
                    backgroundColor: dataSet.color.background,
                    borderWidth: 2,
                    fill: false,
                    tension: 0.2,
                    pointRadius: 0
                };
            });

            charts.psdChart = new Chart(ctx, {
                type: 'line',
                data: { datasets },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top',
                            labels: {
                                usePointStyle: true,
                                padding: 15,
                                font: { size: 11 }
                            }
                        }
                    },
                    scales: {
                        x: {
                            type: 'linear',
                            title: { display: true, text: '频率 (Hz)' }
                        },
                        y: {
                            title: { display: true, text: '功率 (dB)' }
                        }
                    },
                    animation: { duration: 1000, easing: 'easeInOutQuart' },
                    plugins: {
                        zoom: {
                            zoom: {
                                wheel: { enabled: true },
                                pinch: { enabled: true },
                                mode: 'xy',
                            },
                            pan: { enabled: true, mode: 'xy' }
                        }
                    }
                }
            });
        }

        // 更新功率谱密度图（单数据集）
        function updatePSDChart(data) {
            const psdData = computePSD(data);
            const canvas = document.getElementById('psdChartCanvas');
            const ctx = canvas.getContext('2d');

            if (charts.psdChart) {
                charts.psdChart.destroy();
            }

            charts.psdChart = new Chart(ctx, {
                type: 'line',
                data: {
                    datasets: [{
                        label: '功率谱密度',
                        data: psdData,
                        borderColor: '#f59e0b',
                        backgroundColor: 'rgba(245, 158, 11, 0.1)',
                        borderWidth: 2,
                        fill: true,
                        tension: 0.2,
                        pointRadius: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { legend: { display: false } },
                    scales: {
                        x: {
                            type: 'linear',
                            title: { display: true, text: '频率 (Hz)' }
                        },
                        y: {
                            title: { display: true, text: '功率 (dB)' }
                        }
                    },
                    animation: { duration: 1000, easing: 'easeInOutQuart' },
                    plugins: {
                        zoom: {
                            zoom: {
                                wheel: { enabled: true },
                                pinch: { enabled: true },
                                mode: 'xy',
                            },
                            pan: { enabled: true, mode: 'xy' }
                        }
                    }
                }
            });
        }

        // 更新瞬时相位图（多数据集）
        function updatePhaseChartWithMultipleDataSets() {
            const canvas = document.getElementById('phaseChartCanvas');
            const ctx = canvas.getContext('2d');

            if (charts.phaseChart) {
                charts.phaseChart.destroy();
            }

            const datasets = multipleDataSets.map((dataSet, index) => {
                const phaseData = computeInstantaneousPhase(dataSet.data);
                return {
                    label: `${dataSet.fileName} 相位`,
                    data: phaseData,
                    borderColor: dataSet.color.border,
                    backgroundColor: dataSet.color.background,
                    borderWidth: 2,
                    fill: false,
                    tension: 0.3,
                    pointRadius: 0
                };
            });

            charts.phaseChart = new Chart(ctx, {
                type: 'line',
                data: { datasets },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top',
                            labels: {
                                usePointStyle: true,
                                padding: 15,
                                font: { size: 11 }
                            }
                        }
                    },
                    scales: {
                        x: {
                            type: 'linear',
                            title: { display: true, text: '时间 (s)' }
                        },
                        y: {
                            title: { display: true, text: '相位 (弧度)' }
                        }
                    },
                    animation: { duration: 1000, easing: 'easeInOutQuart' },
                    plugins: {
                        zoom: {
                            zoom: {
                                wheel: { enabled: true },
                                pinch: { enabled: true },
                                mode: 'xy',
                            },
                            pan: { enabled: true, mode: 'xy' }
                        }
                    }
                }
            });
        }

        // 更新瞬时相位图（单数据集）
        function updatePhaseChart(data) {
            const phaseData = computeInstantaneousPhase(data);
            const canvas = document.getElementById('phaseChartCanvas');
            const ctx = canvas.getContext('2d');

            if (charts.phaseChart) {
                charts.phaseChart.destroy();
            }

            charts.phaseChart = new Chart(ctx, {
                type: 'line',
                data: {
                    datasets: [{
                        label: '瞬时相位',
                        data: phaseData,
                        borderColor: '#8b5cf6',
                        backgroundColor: 'rgba(139, 92, 246, 0.1)',
                        borderWidth: 2,
                        fill: false,
                        tension: 0.3,
                        pointRadius: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { legend: { display: false } },
                    scales: {
                        x: {
                            type: 'linear',
                            title: { display: true, text: '时间 (s)' }
                        },
                        y: {
                            title: { display: true, text: '相位 (弧度)' }
                        }
                    },
                    animation: { duration: 1000, easing: 'easeInOutQuart' },
                    plugins: {
                        zoom: {
                            zoom: {
                                wheel: { enabled: true },
                                pinch: { enabled: true },
                                mode: 'xy',
                            },
                            pan: { enabled: true, mode: 'xy' }
                        }
                    }
                }
            });
        }

        // 更新面积图（多数据集）
        function updateAreaChartWithMultipleDataSets() {
            const canvas = document.getElementById('areaChartCanvas');
            const ctx = canvas.getContext('2d');

            if (charts.areaChart) {
                charts.areaChart.destroy();
            }

            const datasets = multipleDataSets.map((dataSet, index) => ({
                label: dataSet.fileName,
                data: dataSet.data,
                borderColor: dataSet.color.border,
                backgroundColor: dataSet.color.background.replace('0.1', '0.3'),
                borderWidth: 2,
                fill: 'origin',
                tension: 0.4,
                pointRadius: 0
            }));

            charts.areaChart = new Chart(ctx, {
                type: 'line',
                data: { datasets },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top',
                            labels: {
                                usePointStyle: true,
                                padding: 15,
                                font: { size: 11 }
                            }
                        }
                    },
                    scales: {
                        x: {
                            type: 'linear',
                            title: { display: true, text: '时间 (s)' }
                        },
                        y: { title: { display: true, text: '幅度' } }
                    },
                    animation: { duration: 1000, easing: 'easeInOutQuart' },
                    plugins: {
                        zoom: {
                            zoom: {
                                wheel: { enabled: true },
                                pinch: { enabled: true },
                                mode: 'xy',
                            },
                            pan: { enabled: true, mode: 'xy' }
                        }
                    }
                }
            });
        }

        // 更新面积图（单数据集）
        function updateAreaChart(data) {
            const canvas = document.getElementById('areaChartCanvas');
            const ctx = canvas.getContext('2d');

            if (charts.areaChart) {
                charts.areaChart.destroy();
            }

            charts.areaChart = new Chart(ctx, {
                type: 'line',
                data: {
                    datasets: [{
                        label: '累积面积',
                        data: data,
                        borderColor: '#10b981',
                        backgroundColor: 'rgba(16, 185, 129, 0.3)',
                        borderWidth: 2,
                        fill: 'origin',
                        tension: 0.4,
                        pointRadius: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { legend: { display: false } },
                    scales: {
                        x: {
                            type: 'linear',
                            title: { display: true, text: '时间 (s)' }
                        },
                        y: { title: { display: true, text: '幅度' } }
                    },
                    animation: { duration: 1000, easing: 'easeInOutQuart' },
                    plugins: {
                        zoom: {
                            zoom: {
                                wheel: { enabled: true },
                                pinch: { enabled: true },
                                mode: 'xy',
                            },
                            pan: { enabled: true, mode: 'xy' }
                        }
                    }
                }
            });
        }

        // 更新信号运算图
        function updateOperationChart() {
            if (multipleDataSets.length < 2) {
                // 如果数据集少于2个，显示提示
                const canvas = document.getElementById('operationChartCanvas');
                const ctx = canvas.getContext('2d');

                if (charts.operationChart) {
                    charts.operationChart.destroy();
                }

                // 创建空图表显示提示
                charts.operationChart = new Chart(ctx, {
                    type: 'line',
                    data: { datasets: [] },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: { display: false }
                        }
                    }
                });
                return;
            }

            const operation = document.getElementById('operationSelect').value;
            const data1 = multipleDataSets[0].data;
            const data2 = multipleDataSets[1].data;

            const resultData = performOperation(data1, data2, operation);

            const canvas = document.getElementById('operationChartCanvas');
            const ctx = canvas.getContext('2d');

            if (charts.operationChart) {
                charts.operationChart.destroy();
            }

            charts.operationChart = new Chart(ctx, {
                type: 'line',
                data: {
                    datasets: [{
                        label: `${operation} 运算结果`,
                        data: resultData,
                        borderColor: '#ef4444',
                        backgroundColor: 'rgba(239, 68, 68, 0.1)',
                        borderWidth: 2,
                        fill: false,
                        tension: 0.4,
                        pointRadius: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { legend: { display: false } },
                    scales: {
                        x: {
                            type: 'linear',
                            title: { display: true, text: '时间 (s)' }
                        },
                        y: {
                            title: { display: true, text: '幅度' }
                        }
                    },
                    animation: { duration: 1000, easing: 'easeInOutQuart' }
                }
            });
        }

        // 更新相关图
        function updateCorrelationChart() {
            const correlationType = document.getElementById('correlationSelect').value;
            let corrData;

            if (correlationType === 'autocorr') {
                // 自相关 - 使用第一个数据集
                corrData = computeAutocorrelation(multipleDataSets[0]?.data || currentData);
            } else {
                // 互相关 - 需要两个数据集
                if (multipleDataSets.length < 2) {
                    corrData = computeAutocorrelation(multipleDataSets[0]?.data || currentData);
                } else {
                    corrData = computeCrosscorrelation(multipleDataSets[0].data, multipleDataSets[1].data);
                }
            }

            const canvas = document.getElementById('correlationChartCanvas');
            const ctx = canvas.getContext('2d');

            if (charts.correlationChart) {
                charts.correlationChart.destroy();
            }

            charts.correlationChart = new Chart(ctx, {
                type: 'line',
                data: {
                    datasets: [{
                        label: correlationType === 'autocorr' ? '自相关' : '互相关',
                        data: corrData,
                        borderColor: '#06b6d4',
                        backgroundColor: 'rgba(6, 182, 212, 0.1)',
                        borderWidth: 2,
                        fill: true,
                        tension: 0.3,
                        pointRadius: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { legend: { display: false } },
                    scales: {
                        x: {
                            type: 'linear',
                            title: { display: true, text: '滞后 (样本)' }
                        },
                        y: {
                            title: { display: true, text: '相关系数' }
                        }
                    },
                    animation: { duration: 1000, easing: 'easeInOutQuart' }
                }
            });
        }

        // 更新累积分布函数图
        function updateCDFChart(data) {
            const cdfData = computeCDF(data);
            const canvas = document.getElementById('cdfChartCanvas');
            const ctx = canvas.getContext('2d');

            if (charts.cdfChart) {
                charts.cdfChart.destroy();
            }

            charts.cdfChart = new Chart(ctx, {
                type: 'line',
                data: {
                    datasets: [{
                        label: '累积分布函数',
                        data: cdfData,
                        borderColor: '#84cc16',
                        backgroundColor: 'rgba(132, 204, 22, 0.1)',
                        borderWidth: 2,
                        fill: true,
                        tension: 0.2,
                        pointRadius: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { legend: { display: false } },
                    scales: {
                        x: {
                            type: 'linear',
                            title: { display: true, text: '数值' }
                        },
                        y: {
                            type: 'linear',
                            min: 0,
                            max: 1,
                            title: { display: true, text: '累积概率' }
                        }
                    },
                    animation: { duration: 1000, easing: 'easeInOutQuart' },
                    plugins: {
                        zoom: {
                            zoom: {
                                wheel: { enabled: true },
                                pinch: { enabled: true },
                                mode: 'xy',
                            },
                            pan: { enabled: true, mode: 'xy' }
                        }
                    }
                }
            });
        }

        // 更新小波变换图
        function updateWaveletChart(data) {
            const waveletType = document.getElementById('waveletSelect').value;
            const waveletData = computeWavelet(data, waveletType);
            const canvas = document.getElementById('waveletChartCanvas');
            const ctx = canvas.getContext('2d');

            if (charts.waveletChart) {
                charts.waveletChart.destroy();
            }

            charts.waveletChart = new Chart(ctx, {
                type: 'line',
                data: {
                    datasets: [{
                        label: `${waveletType} 小波变换`,
                        data: waveletData,
                        borderColor: '#a855f7',
                        backgroundColor: 'rgba(168, 85, 247, 0.1)',
                        borderWidth: 2,
                        fill: false,
                        tension: 0.3,
                        pointRadius: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { legend: { display: false } },
                    scales: {
                        x: {
                            type: 'linear',
                            title: { display: true, text: '时间 (s)' }
                        },
                        y: {
                            title: { display: true, text: '小波系数' }
                        }
                    },
                    animation: { duration: 1000, easing: 'easeInOutQuart' }
                }
            });
        }

        // 数学计算函数
        function computeFFT(data) {
            const N = Math.min(data.length, 512);
            const fftData = [];

            // 从数据的时间步长计算实际采样频率
            let sampleRate = 1000; // 默认1kHz
            if (data.length > 1) {
                const timeStep = data[1].x - data[0].x;
                if (timeStep > 0) {
                    sampleRate = 1 / timeStep;
                }
            }

            for (let k = 0; k < N/2; k++) {
                let real = 0, imag = 0;
                for (let n = 0; n < N; n++) {
                    const angle = -2 * Math.PI * k * n / N;
                    real += data[n].y * Math.cos(angle);
                    imag += data[n].y * Math.sin(angle);
                }
                const magnitude = Math.sqrt(real * real + imag * imag) / N;
                const frequency = k * sampleRate / N;
                fftData.push({ x: frequency, y: magnitude });
            }

            return fftData;
        }

        function computeHistogram(data, bins = 20) {
            const values = data.map(d => d.y);
            const min = Math.min(...values);
            const max = Math.max(...values);
            const binWidth = (max - min) / bins;

            const histogram = new Array(bins).fill(0);
            const labels = [];

            for (let i = 0; i < bins; i++) {
                labels.push((min + i * binWidth).toFixed(2));
            }

            values.forEach(value => {
                const binIndex = Math.min(Math.floor((value - min) / binWidth), bins - 1);
                histogram[binIndex]++;
            });

            return { labels, values: histogram };
        }

        // 计算指定范围的直方图
        function computeHistogramWithRange(data, min, max, bins = 20) {
            const values = data.map(d => d.y);
            const binWidth = (max - min) / bins;

            const histogram = new Array(bins).fill(0);
            const labels = [];

            for (let i = 0; i < bins; i++) {
                labels.push((min + i * binWidth).toFixed(2));
            }

            values.forEach(value => {
                if (value >= min && value <= max) {
                    const binIndex = Math.min(Math.floor((value - min) / binWidth), bins - 1);
                    histogram[binIndex]++;
                }
            });

            return { labels, values: histogram };
        }

        function prepareScatterData(data) {
            const scatterData = [];
            for (let i = 0; i < data.length - 1; i++) {
                scatterData.push({
                    x: data[i].y,
                    y: data[i + 1].y
                });
            }
            return scatterData.slice(0, 200);
        }

        function generateHeatmapData(data) {
            const size = 20;
            const heatmap = [];

            for (let i = 0; i < size; i++) {
                const row = [];
                for (let j = 0; j < size; j++) {
                    const index = Math.floor((i * size + j) * data.length / (size * size));
                    const value = data[index] ? data[index].y : 0;
                    row.push(value + Math.sin(i * 0.5) * Math.cos(j * 0.5) * 0.3);
                }
                heatmap.push(row);
            }

            return heatmap;
        }

        // 计算包络线
        function computeEnvelope(data) {
            const envelopeData = [];
            const windowSize = Math.max(5, Math.floor(data.length / 100));

            for (let i = 0; i < data.length; i++) {
                let maxVal = -Infinity;
                let minVal = Infinity;

                const start = Math.max(0, i - windowSize);
                const end = Math.min(data.length - 1, i + windowSize);

                for (let j = start; j <= end; j++) {
                    maxVal = Math.max(maxVal, Math.abs(data[j].y));
                    minVal = Math.min(minVal, Math.abs(data[j].y));
                }

                envelopeData.push({ x: data[i].x, y: maxVal });
            }

            return envelopeData;
        }

        // 计算功率谱密度
        function computePSD(data) {
            const fftData = computeFFT(data);
            const psdData = [];

            fftData.forEach(point => {
                const psd = point.y * point.y; // 功率 = 幅度的平方
                psdData.push({ x: point.x, y: 10 * Math.log10(psd + 1e-10) }); // 转换为dB
            });

            return psdData;
        }

        // 计算瞬时相位
        function computeInstantaneousPhase(data) {
            const phaseData = [];

            for (let i = 1; i < data.length; i++) {
                const dy = data[i].y - data[i-1].y;
                const dx = data[i].x - data[i-1].x;
                const phase = Math.atan2(dy, dx);
                phaseData.push({ x: data[i].x, y: phase });
            }

            return phaseData;
        }

        // 计算累积分布函数
        function computeCDF(data) {
            const values = data.map(d => d.y).sort((a, b) => a - b);
            const cdfData = [];

            values.forEach((value, index) => {
                const probability = (index + 1) / values.length;
                cdfData.push({ x: value, y: probability });
            });

            return cdfData;
        }

        // 计算自相关
        function computeAutocorrelation(data) {
            const corrData = [];
            const N = Math.min(data.length, 256);

            for (let lag = 0; lag < N/2; lag++) {
                let sum = 0;
                let count = 0;

                for (let i = 0; i < N - lag; i++) {
                    sum += data[i].y * data[i + lag].y;
                    count++;
                }

                const correlation = count > 0 ? sum / count : 0;
                corrData.push({ x: lag, y: correlation });
            }

            return corrData;
        }

        // 计算互相关
        function computeCrosscorrelation(data1, data2) {
            const corrData = [];
            const N = Math.min(data1.length, data2.length, 256);

            for (let lag = -N/2; lag < N/2; lag++) {
                let sum = 0;
                let count = 0;

                for (let i = Math.max(0, -lag); i < Math.min(N, N - lag); i++) {
                    if (i + lag >= 0 && i + lag < N) {
                        sum += data1[i].y * data2[i + lag].y;
                        count++;
                    }
                }

                const correlation = count > 0 ? sum / count : 0;
                corrData.push({ x: lag, y: correlation });
            }

            return corrData;
        }

        // 执行信号运算
        function performOperation(data1, data2, operation) {
            const resultData = [];
            const minLength = Math.min(data1.length, data2.length);

            for (let i = 0; i < minLength; i++) {
                let result;
                switch (operation) {
                    case 'add':
                        result = data1[i].y + data2[i].y;
                        break;
                    case 'subtract':
                        result = data1[i].y - data2[i].y;
                        break;
                    case 'multiply':
                        result = data1[i].y * data2[i].y;
                        break;
                    case 'divide':
                        result = data2[i].y !== 0 ? data1[i].y / data2[i].y : 0;
                        break;
                    default:
                        result = data1[i].y;
                }
                resultData.push({ x: data1[i].x, y: result });
            }

            return resultData;
        }

        // 计算小波变换
        function computeWavelet(data, waveletType) {
            const waveletData = [];
            const N = Math.min(data.length, 256);

            for (let i = 0; i < N; i++) {
                let coefficient = 0;

                for (let j = 0; j < N; j++) {
                    let wavelet;
                    const t = (j - i) / 10; // 缩放参数

                    switch (waveletType) {
                        case 'morlet':
                            // Morlet小波
                            wavelet = Math.exp(-t * t / 2) * Math.cos(5 * t);
                            break;
                        case 'mexican':
                            // Mexican Hat小波
                            wavelet = (1 - t * t) * Math.exp(-t * t / 2);
                            break;
                        case 'daubechies':
                            // 简化的Daubechies小波
                            if (Math.abs(t) < 1) {
                                wavelet = Math.sin(Math.PI * t) / (Math.PI * t);
                            } else {
                                wavelet = 0;
                            }
                            break;
                        default:
                            wavelet = Math.exp(-t * t / 2) * Math.cos(5 * t);
                    }

                    coefficient += data[j].y * wavelet;
                }

                waveletData.push({ x: data[i].x, y: coefficient });
            }

            return waveletData;
        }

        // 更新极坐标图
        function updatePolarChart(data) {
            const canvas = document.getElementById('polarChartCanvas');
            const ctx = canvas.getContext('2d');

            if (charts.polarChart) {
                charts.polarChart.destroy();
            }

            // 将数据转换为极坐标形式
            const polarData = data.map((point, index) => {
                const angle = (index / data.length) * 2 * Math.PI; // 角度
                const radius = Math.abs(point.y); // 半径（取绝对值）
                return {
                    x: radius * Math.cos(angle),
                    y: radius * Math.sin(angle),
                    angle: angle * 180 / Math.PI, // 转换为度数
                    radius: radius
                };
            });

            charts.polarChart = new Chart(ctx, {
                type: 'scatter',
                data: {
                    datasets: [{
                        label: '极坐标数据',
                        data: polarData,
                        backgroundColor: 'rgba(59, 130, 246, 0.6)',
                        borderColor: 'rgba(59, 130, 246, 1)',
                        borderWidth: 1,
                        pointRadius: 2
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        x: {
                            type: 'linear',
                            position: 'center',
                            title: { display: true, text: 'X轴' },
                            grid: { color: 'rgba(0, 0, 0, 0.1)' }
                        },
                        y: {
                            type: 'linear',
                            position: 'center',
                            title: { display: true, text: 'Y轴' },
                            grid: { color: 'rgba(0, 0, 0, 0.1)' }
                        }
                    },
                    plugins: {
                        legend: { display: true },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const point = context.raw;
                                    return `角度: ${point.angle.toFixed(1)}°, 半径: ${point.radius.toFixed(3)}`;
                                }
                            }
                        },
                        zoom: {
                            zoom: {
                                wheel: { enabled: true },
                                pinch: { enabled: true },
                                mode: 'xy',
                            },
                            pan: { enabled: true, mode: 'xy' }
                        }
                    }
                }
            });
        }

        // 更新矢量图
        function updateVectorChart(data) {
            const canvas = document.getElementById('vectorChartCanvas');
            const ctx = canvas.getContext('2d');

            if (charts.vectorChart) {
                charts.vectorChart.destroy();
            }

            // 计算矢量数据（使用相邻点的差值作为矢量）
            const vectorData = [];
            const step = Math.max(1, Math.floor(data.length / 50)); // 限制矢量数量

            for (let i = 0; i < data.length - step; i += step) {
                const x = data[i].x;
                const y = data[i].y;
                const dx = data[i + step].x - data[i].x;
                const dy = data[i + step].y - data[i].y;

                // 计算矢量长度和角度
                const magnitude = Math.sqrt(dx * dx + dy * dy);
                const angle = Math.atan2(dy, dx);

                vectorData.push({
                    x: x,
                    y: y,
                    dx: dx,
                    dy: dy,
                    magnitude: magnitude,
                    angle: angle * 180 / Math.PI
                });
            }

            // 创建散点图显示矢量起点
            charts.vectorChart = new Chart(ctx, {
                type: 'scatter',
                data: {
                    datasets: [{
                        label: '矢量起点',
                        data: vectorData.map(v => ({ x: v.x, y: v.y })),
                        backgroundColor: 'rgba(59, 130, 246, 0.6)',
                        borderColor: 'rgba(59, 130, 246, 1)',
                        borderWidth: 1,
                        pointRadius: 3
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        x: {
                            type: 'linear',
                            title: { display: true, text: '时间' },
                            grid: { color: 'rgba(0, 0, 0, 0.1)' }
                        },
                        y: {
                            type: 'linear',
                            title: { display: true, text: '幅值' },
                            grid: { color: 'rgba(0, 0, 0, 0.1)' }
                        }
                    },
                    plugins: {
                        legend: { display: true },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const index = context.dataIndex;
                                    const vector = vectorData[index];
                                    return [
                                        `位置: (${vector.x.toFixed(3)}, ${vector.y.toFixed(3)})`,
                                        `矢量: (${vector.dx.toFixed(3)}, ${vector.dy.toFixed(3)})`,
                                        `长度: ${vector.magnitude.toFixed(3)}`,
                                        `角度: ${vector.angle.toFixed(1)}°`
                                    ];
                                }
                            }
                        },
                        zoom: {
                            zoom: {
                                wheel: { enabled: true },
                                pinch: { enabled: true },
                                mode: 'xy',
                            },
                            pan: { enabled: true, mode: 'xy' }
                        }
                    },
                    // 自定义绘制矢量箭头
                    onHover: function(event, activeElements) {
                        // 可以在这里添加矢量箭头的绘制逻辑
                    }
                }
            });

            // 在图表上绘制矢量箭头
            const originalDraw = charts.vectorChart.draw;
            charts.vectorChart.draw = function() {
                originalDraw.call(this);

                const chartArea = this.chartArea;
                const xScale = this.scales.x;
                const yScale = this.scales.y;

                ctx.save();
                ctx.strokeStyle = 'rgba(239, 68, 68, 0.7)';
                ctx.lineWidth = 1;

                vectorData.forEach(vector => {
                    const startX = xScale.getPixelForValue(vector.x);
                    const startY = yScale.getPixelForValue(vector.y);
                    const endX = xScale.getPixelForValue(vector.x + vector.dx);
                    const endY = yScale.getPixelForValue(vector.y + vector.dy);

                    // 检查是否在图表区域内
                    if (startX >= chartArea.left && startX <= chartArea.right &&
                        startY >= chartArea.top && startY <= chartArea.bottom) {

                        // 绘制矢量线
                        ctx.beginPath();
                        ctx.moveTo(startX, startY);
                        ctx.lineTo(endX, endY);
                        ctx.stroke();

                        // 绘制箭头头部
                        const arrowLength = 5;
                        const arrowAngle = Math.PI / 6;
                        const angle = Math.atan2(endY - startY, endX - startX);

                        ctx.beginPath();
                        ctx.moveTo(endX, endY);
                        ctx.lineTo(
                            endX - arrowLength * Math.cos(angle - arrowAngle),
                            endY - arrowLength * Math.sin(angle - arrowAngle)
                        );
                        ctx.moveTo(endX, endY);
                        ctx.lineTo(
                            endX - arrowLength * Math.cos(angle + arrowAngle),
                            endY - arrowLength * Math.sin(angle + arrowAngle)
                        );
                        ctx.stroke();
                    }
                });

                ctx.restore();
            };
        }

        // 初始化图表操作
        function initChartActions() {
            console.log('initChartActions called');

            // 使用事件委托绑定到document，确保能捕获所有点击
            document.addEventListener('click', function(e) {
                // 检查点击的元素是否是图表按钮
                if (e.target.classList.contains('chart-btn') || e.target.closest('.chart-btn')) {
                    const btn = e.target.classList.contains('chart-btn') ? e.target : e.target.closest('.chart-btn');

                    e.preventDefault();
                    e.stopPropagation();

                    const action = btn.dataset.action;
                    const chartType = btn.dataset.chart;

                    console.log('Chart button clicked:', action, chartType);

                    if (!action || !chartType) {
                        console.error('Button missing data attributes:', btn);
                        showNotification('按钮配置错误', 'error');
                        return;
                    }

                    // 处理不同的操作
                    switch (action) {
                        case 'zoom-x-in':
                            console.log('Executing zoom-x-in');
                            zoomChart(chartType + 'Chart', 'x', 'in');
                            break;
                        case 'zoom-x-out':
                            console.log('Executing zoom-x-out');
                            zoomChart(chartType + 'Chart', 'x', 'out');
                            break;
                        case 'zoom-y-in':
                            console.log('Executing zoom-y-in');
                            zoomChart(chartType + 'Chart', 'y', 'in');
                            break;
                        case 'zoom-y-out':
                            console.log('Executing zoom-y-out');
                            zoomChart(chartType + 'Chart', 'y', 'out');
                            break;
                        case 'auto':
                            console.log('Executing auto scale');
                            autoScaleChart(chartType + 'Chart');
                            break;
                        case 'export':
                            console.log('Executing export PNG');
                            exportChart(chartType, 'png');
                            break;
                        case 'export-jpg':
                            console.log('Executing export JPG');
                            exportChart(chartType, 'jpg');
                            break;
                        case 'export-excel':
                            console.log('Executing export Excel');
                            exportChartToExcel(chartType);
                            break;
                        case 'fullscreen':
                            console.log('Executing fullscreen');
                            showFullscreen(chartType);
                            break;
                        default:
                            console.log('Unknown action:', action);
                            showNotification('未知操作', 'warning');
                    }
                }
            });

            // 其他非图表按钮的事件绑定
            setTimeout(() => {



                // 导出全部和刷新按钮
                const exportAllBtn = document.getElementById('exportAllBtn');
                if (exportAllBtn) {
                    exportAllBtn.addEventListener('click', exportAllCharts);
                }

                const refreshAllBtn = document.getElementById('refreshAllBtn');
                if (refreshAllBtn) {
                    refreshAllBtn.addEventListener('click', () => {
                        if (currentData) {
                            updateAllCharts(currentData, '刷新数据');
                            showNotification('数据已刷新', 'success');
                        }
                    });
                }

                // 选择框事件监听器
                const operationSelect = document.getElementById('operationSelect');
                if (operationSelect) {
                    operationSelect.addEventListener('change', () => {
                        if (multipleDataSets.length >= 2) {
                            updateOperationChart();
                        }
                    });
                }

                const correlationSelect = document.getElementById('correlationSelect');
                if (correlationSelect) {
                    correlationSelect.addEventListener('change', () => {
                        updateCorrelationChart();
                    });
                }

                const waveletSelect = document.getElementById('waveletSelect');
                if (waveletSelect) {
                    waveletSelect.addEventListener('change', () => {
                        if (currentData) {
                            updateWaveletChart(currentData);
                        }
                    });
                }

                // 应用噪声按钮 - 使用事件委托
                document.addEventListener('click', function(e) {
                    if (e.target && e.target.id === 'applyNoiseBtn') {
                        console.log('Noise button clicked via delegation!');
                        e.preventDefault();
                        e.stopPropagation();
                        addNoise();
                    }
                });

            }, 100); // 延迟100ms确保DOM加载完成
        }

        // 导出图表
        function exportChart(chartType, format) {
            const chart = charts[chartType + 'Chart'];
            if (!chart) {
                showNotification('图表未找到', 'error');
                return;
            }

            const canvas = chart.canvas;
            const link = document.createElement('a');
            link.download = `${chartType}_chart_${new Date().getTime()}.${format}`;

            if (format === 'png') {
                link.href = canvas.toDataURL('image/png');
            } else if (format === 'jpg') {
                link.href = canvas.toDataURL('image/jpeg', 0.9);
            }

            link.click();
            showNotification(`${chartType}图表已导出为${format.toUpperCase()}`, 'success');
        }

        // 导出图表数据到Excel
        function exportChartToExcel(chartType) {
            const chart = charts[chartType + 'Chart'];
            if (!chart) {
                showNotification('图表未找到', 'error');
                return;
            }

            try {
                const workbook = XLSX.utils.book_new();
                const chartData = chart.data;

                // 准备数据
                let excelData = [];

                if (chartType === 'time') {
                    // 时域数据
                    excelData.push(['时间(s)', '幅值']);
                    if (currentData && currentData.length > 0) {
                        currentData.forEach((point, index) => {
                            excelData.push([point.x, point.y]);
                        });
                    }
                } else if (chartType === 'frequency') {
                    // 频域数据
                    excelData.push(['频率(Hz)', '幅值']);
                    if (chartData.datasets && chartData.datasets[0] && chartData.datasets[0].data) {
                        chartData.datasets[0].data.forEach((point, index) => {
                            excelData.push([point.x, point.y]);
                        });
                    }
                } else if (chartType === 'bar') {
                    // 数据分布
                    excelData.push(['区间', '频次']);
                    if (chartData.labels && chartData.datasets && chartData.datasets[0]) {
                        chartData.labels.forEach((label, index) => {
                            excelData.push([label, chartData.datasets[0].data[index]]);
                        });
                    }
                } else if (chartType === 'envelope') {
                    // 包络线数据
                    excelData.push(['时间(s)', '包络值']);
                    if (chartData.datasets && chartData.datasets[0] && chartData.datasets[0].data) {
                        chartData.datasets[0].data.forEach((point, index) => {
                            excelData.push([point.x, point.y]);
                        });
                    }
                } else if (chartType === 'psd') {
                    // 功率谱密度
                    excelData.push(['频率(Hz)', '功率谱密度']);
                    if (chartData.datasets && chartData.datasets[0] && chartData.datasets[0].data) {
                        chartData.datasets[0].data.forEach((point, index) => {
                            excelData.push([point.x, point.y]);
                        });
                    }
                } else if (chartType === 'phase') {
                    // 瞬时相位
                    excelData.push(['时间(s)', '相位(弧度)']);
                    if (chartData.datasets && chartData.datasets[0] && chartData.datasets[0].data) {
                        chartData.datasets[0].data.forEach((point, index) => {
                            excelData.push([point.x, point.y]);
                        });
                    }
                } else if (chartType === 'operation') {
                    // 信号运算结果
                    const operation = document.getElementById('operationSelect').value;
                    excelData.push(['时间(s)', `运算结果(${operation})`]);
                    if (chartData.datasets && chartData.datasets[0] && chartData.datasets[0].data) {
                        chartData.datasets[0].data.forEach((point, index) => {
                            excelData.push([point.x, point.y]);
                        });
                    }
                } else if (chartType === 'correlation') {
                    // 相关分析
                    const corrType = document.getElementById('correlationSelect').value;
                    excelData.push(['延迟', `相关值(${corrType})`]);
                    if (chartData.datasets && chartData.datasets[0] && chartData.datasets[0].data) {
                        chartData.datasets[0].data.forEach((point, index) => {
                            excelData.push([point.x, point.y]);
                        });
                    }
                } else if (chartType === 'cdf') {
                    // 累积分布函数
                    excelData.push(['数值', '累积概率']);
                    if (chartData.datasets && chartData.datasets[0] && chartData.datasets[0].data) {
                        chartData.datasets[0].data.forEach((point, index) => {
                            excelData.push([point.x, point.y]);
                        });
                    }
                } else if (chartType === 'wavelet') {
                    // 小波变换
                    const waveletType = document.getElementById('waveletSelect').value;
                    excelData.push(['时间(s)', `小波系数(${waveletType})`]);
                    if (chartData.datasets && chartData.datasets[0] && chartData.datasets[0].data) {
                        chartData.datasets[0].data.forEach((point, index) => {
                            excelData.push([point.x, point.y]);
                        });
                    }
                }

                // 创建工作表
                const worksheet = XLSX.utils.aoa_to_sheet(excelData);

                // 设置列宽
                worksheet['!cols'] = [
                    { width: 15 },
                    { width: 15 }
                ];

                // 添加工作表到工作簿
                const chartName = getChartDisplayName(chartType);
                XLSX.utils.book_append_sheet(workbook, worksheet, chartName);

                // 导出文件
                const fileName = `${chartName}_数据_${new Date().getTime()}.xlsx`;
                XLSX.writeFile(workbook, fileName);

                showNotification(`${chartName}数据已导出到Excel`, 'success');
            } catch (error) {
                console.error('Excel导出错误:', error);
                showNotification('Excel导出失败', 'error');
            }
        }

        // 获取图表显示名称
        function getChartDisplayName(chartType) {
            const names = {
                'time': '时域波形',
                'frequency': '频域分析',
                'bar': '数据分布',
                'envelope': '包络线',
                'psd': '功率谱密度',
                'phase': '瞬时相位',
                'operation': '信号运算',
                'correlation': '相关分析',
                'cdf': '累积分布函数',
                'wavelet': '小波变换',
                'polar': '极坐标',
                'vector': '矢量'
            };
            return names[chartType] || '图表';
        }

        // 导出所有图表
        function exportAllCharts() {
            const chartTypes = ['time', 'frequency', 'bar', 'envelope', 'psd', 'phase', 'operation', 'correlation', 'cdf', 'wavelet', 'polar', 'vector'];
            chartTypes.forEach((type, index) => {
                setTimeout(() => {
                    exportChart(type, 'png');
                }, index * 200);
            });
            showNotification('所有图表导出完成', 'success');
        }

        // 图表缩放功能
        function zoomChart(chartId, axis, direction) {
            const chartType = chartId.replace('Chart', '');
            const chart = charts[chartId];

            if (!chart) {
                showNotification(`图表未找到，请先加载数据`, 'warning');
                return;
            }

            if (!chart.options || !chart.options.scales) {
                showNotification('图表不支持缩放功能', 'warning');
                return;
            }

            const scale = chart.options.scales[axis];
            if (!scale) return;

            // 获取当前范围
            let currentMin = scale.min;
            let currentMax = scale.max;

            // 如果没有设置范围，使用数据范围
            if (currentMin === undefined || currentMax === undefined) {
                const data = chart.data.datasets[0].data;
                if (axis === 'x') {
                    const xValues = data.map(d => d.x).filter(x => !isNaN(x) && isFinite(x));
                    currentMin = currentMin || Math.min(...xValues);
                    currentMax = currentMax || Math.max(...xValues);
                } else {
                    const yValues = data.map(d => d.y).filter(y => !isNaN(y) && isFinite(y));
                    currentMin = currentMin || Math.min(...yValues);
                    currentMax = currentMax || Math.max(...yValues);
                }
            }

            const range = currentMax - currentMin;
            const center = (currentMin + currentMax) / 2;

            // 缩放因子
            const zoomFactor = direction === 'in' ? 0.8 : 1.25;
            const newRange = range * zoomFactor;

            // 设置新的范围
            scale.min = center - newRange / 2;
            scale.max = center + newRange / 2;

            // 更新图表
            chart.update('none');

            // 显示通知
            const axisName = axis === 'x' ? '横轴' : '纵轴';
            const actionName = direction === 'in' ? '放大' : '缩小';
            showNotification(`${getChartName(chartType)}${axisName}已${actionName}`, 'success');
        }

        // 全屏显示
        function showFullscreen(chartType) {
            showNotification('全屏功能开发中...', 'info');
        }



        function addNoise() {
            if (!currentData) {
                showNotification('请先加载数据', 'warning');
                return;
            }

            // 添加按钮点击反馈
            const applyNoiseBtn = document.getElementById('applyNoiseBtn');
            if (applyNoiseBtn) {
                applyNoiseBtn.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    applyNoiseBtn.style.transform = '';
                }, 150);
            }

            // 检查是否有噪声配置面板
            const noiseConfigPanel = document.getElementById('noiseConfigPanel');
            const noiseAmpSlider = document.getElementById('noiseAmpSlider');

            let selectedNoiseTypes = ['gaussian']; // 默认高斯噪声
            let noiseAmplitude = 0.2; // 默认幅度

            // 如果有配置面板，获取用户选择
            if (noiseConfigPanel && noiseConfigPanel.style.display !== 'none') {
                selectedNoiseTypes = [];
                document.querySelectorAll('.noise-checkbox:checked').forEach(cb => {
                    selectedNoiseTypes.push(cb.id.replace('Noise', ''));
                });

                if (selectedNoiseTypes.length === 0) {
                    showNotification('请选择至少一种噪声类型', 'warning');
                    return;
                }

                if (noiseAmpSlider) {
                    noiseAmplitude = parseFloat(noiseAmpSlider.value);
                }
            }

            // 添加多种噪声
            const noisyData = currentData.map((point, i) => {
                let noiseValue = 0;

                selectedNoiseTypes.forEach(noiseType => {
                    switch (noiseType) {
                        case 'gaussian':
                            const u1 = Math.random();
                            const u2 = Math.random();
                            const gaussian = Math.sqrt(-2 * Math.log(u1)) * Math.cos(2 * Math.PI * u2);
                            noiseValue += gaussian * noiseAmplitude * 0.3;
                            break;

                        case 'impulse':
                            if (Math.random() < 0.05) {
                                noiseValue += (Math.random() > 0.5 ? 1 : -1) * noiseAmplitude * 2;
                            }
                            break;

                        case 'white':
                            noiseValue += (Math.random() - 0.5) * noiseAmplitude;
                            break;

                        case 'flicker':
                            const flickerFreq = 1 / (i + 1);
                            noiseValue += Math.sin(2 * Math.PI * flickerFreq * point.x) * noiseAmplitude * 0.5;
                            break;

                        case 'thermal':
                            const thermal = Math.sqrt(-2 * Math.log(Math.random())) * Math.cos(2 * Math.PI * Math.random());
                            noiseValue += thermal * noiseAmplitude * 0.1;
                            break;

                        case 'colored':
                            const coloredFreq = 100 * (0.1 + 0.9 * Math.random());
                            noiseValue += Math.sin(2 * Math.PI * coloredFreq * point.x) * noiseAmplitude * 0.4;
                            break;
                    }
                });

                return {
                    x: point.x,
                    y: point.y + noiseValue
                };
            });

            const noiseNames = {
                'gaussian': '高斯',
                'impulse': '脉冲',
                'white': '白',
                'flicker': '闪烁',
                'thermal': '热',
                'colored': '色'
            };
            const noiseTypeNames = selectedNoiseTypes.map(type => noiseNames[type]).join('+');

            loadData(noisyData, `添加${noiseTypeNames}噪声后的数据`);
            showNotification(`已添加${noiseTypeNames}噪声`, 'success');
        }

        function denoise() {
            if (!currentData) return;

            // 移动平均去噪
            const windowSize = 5;
            const denoisedData = [];

            for (let i = 0; i < currentData.length; i++) {
                let sum = 0;
                let count = 0;

                for (let j = Math.max(0, i - windowSize); j <= Math.min(currentData.length - 1, i + windowSize); j++) {
                    sum += currentData[j].y;
                    count++;
                }

                denoisedData.push({
                    x: currentData[i].x,
                    y: sum / count
                });
            }

            loadData(denoisedData, '去噪处理后的数据');
            showNotification('去噪处理完成', 'success');
        }

        // 历史数据管理
        function addToHistory(data, name, type) {
            const historyItem = {
                data: data,
                name: name,
                type: type,
                timestamp: Date.now()
            };

            historyData.unshift(historyItem);

            if (historyData.length > 10) {
                historyData = historyData.slice(0, 10);
            }

            localStorage.setItem('dataHistory', JSON.stringify(historyData));
        }

        function showHistoryPanel() {
            if (historyData.length === 0) {
                showNotification('暂无历史数据', 'info');
                return;
            }

            let historyHtml = '<div style="position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 30px; border-radius: 16px; box-shadow: 0 20px 60px rgba(0,0,0,0.2); z-index: 10000; max-width: 500px; max-height: 70vh; overflow-y: auto;">';
            historyHtml += '<h3 style="margin-bottom: 20px; color: #1e293b;">历史数据</h3>';

            historyData.forEach((item, index) => {
                historyHtml += `
                    <div style="padding: 15px; border: 1px solid #e2e8f0; border-radius: 8px; margin-bottom: 10px; cursor: pointer;" onclick="loadHistoryData(${index})">
                        <h4 style="margin: 0 0 5px 0; color: #1e293b;">${item.name}</h4>
                        <p style="margin: 0; font-size: 12px; color: #64748b;">${new Date(item.timestamp).toLocaleString()}</p>
                    </div>
                `;
            });

            historyHtml += '<button onclick="closeHistoryPanel()" style="margin-top: 20px; padding: 10px 20px; background: #3b82f6; color: white; border: none; border-radius: 8px; cursor: pointer;">关闭</button>';
            historyHtml += '</div>';
            historyHtml += '<div id="historyOverlay" onclick="closeHistoryPanel()" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 9999;"></div>';

            document.body.insertAdjacentHTML('beforeend', historyHtml);
        }

        function loadHistoryData(index) {
            const item = historyData[index];
            if (item) {
                loadData(item.data, item.name);
                closeHistoryPanel();
                showNotification('历史数据加载成功', 'success');
            }
        }

        function closeHistoryPanel() {
            const overlay = document.getElementById('historyOverlay');
            if (overlay) {
                overlay.parentElement.removeChild(overlay.previousElementSibling);
                overlay.parentElement.removeChild(overlay);
            }
        }

        // 通知系统
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed; top: 20px; right: 20px; padding: 15px 25px;
                border-radius: 12px; color: white; font-weight: 600; z-index: 10000;
                transform: translateX(100%); transition: transform 0.3s ease;
                box-shadow: 0 8px 25px rgba(0,0,0,0.15); backdrop-filter: blur(20px);
                font-size: 14px; max-width: 300px;
            `;

            switch(type) {
                case 'success':
                    notification.style.background = 'linear-gradient(135deg, #10b981, #059669)';
                    break;
                case 'error':
                    notification.style.background = 'linear-gradient(135deg, #ef4444, #dc2626)';
                    break;
                case 'warning':
                    notification.style.background = 'linear-gradient(135deg, #f59e0b, #d97706)';
                    break;
                default:
                    notification.style.background = 'linear-gradient(135deg, #3b82f6, #2563eb)';
            }

            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => notification.style.transform = 'translateX(0)', 100);
            setTimeout(() => {
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => document.body.removeChild(notification), 300);
            }, 3000);
        }

        // 全局函数（供HTML调用）
        window.loadHistoryData = loadHistoryData;
        window.closeHistoryPanel = closeHistoryPanel;
        window.removeDataSet = removeDataSet;
    </script>

    <!-- 固定水平滚动条 -->
    <div class="horizontal-scrollbar" id="horizontalScrollbar">
        <div class="horizontal-scrollbar-content"></div>
    </div>

    <script>
        // 同步水平滚动条与主内容区域
        document.addEventListener('DOMContentLoaded', function() {
            const mainContent = document.querySelector('.main-content');
            const horizontalScrollbar = document.getElementById('horizontalScrollbar');

            console.log('水平滚动条元素:', horizontalScrollbar);
            console.log('主内容区域:', mainContent);

            if (mainContent && horizontalScrollbar) {
                console.log('水平滚动条同步功能已启用');

                // 确保水平滚动条可见
                horizontalScrollbar.style.display = 'block';
                horizontalScrollbar.style.visibility = 'visible';

                // 主内容区域水平滚动时，同步水平滚动条
                mainContent.addEventListener('scroll', function() {
                    horizontalScrollbar.scrollLeft = mainContent.scrollLeft;
                });

                // 水平滚动条滚动时，同步主内容区域
                horizontalScrollbar.addEventListener('scroll', function() {
                    mainContent.scrollLeft = horizontalScrollbar.scrollLeft;
                });
            } else {
                console.error('无法找到水平滚动条或主内容区域元素');
            }
        });
    </script>
</body>
</html>
