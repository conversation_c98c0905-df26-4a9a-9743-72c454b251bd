<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据分析平台 - 简化版</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: 500;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .chart-container {
            width: 100%;
            height: 400px;
            margin: 20px 0;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            background: #f8f9fa;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 8px;
            font-size: 14px;
            transition: background 0.3s;
        }
        button:hover {
            background: #0056b3;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
            margin: 20px 0;
        }
        .loading {
            text-align: center;
            padding: 40px;
            font-size: 18px;
            color: #6c757d;
        }
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>数据分析平台 - 简化版测试</h1>
            <p>用于测试Chart.js功能和诊断加载问题</p>
        </div>

        <div id="loadingStatus" class="status warning">正在初始化...</div>

        <div class="controls">
            <button onclick="testChartJS()">测试Chart.js</button>
            <button onclick="createTestCharts()">创建测试图表</button>
            <button onclick="loadSampleData()">加载示例数据</button>
            <button onclick="clearAll()">清空所有</button>
        </div>

        <div class="grid">
            <div class="chart-container">
                <h3>时域波形图</h3>
                <canvas id="timeChart" width="400" height="300"></canvas>
            </div>
            <div class="chart-container">
                <h3>频域分析图</h3>
                <canvas id="frequencyChart" width="400" height="300"></canvas>
            </div>
        </div>

        <div class="log-section">
            <h3>系统日志</h3>
            <div id="systemLog" class="log">系统启动中...</div>
        </div>
    </div>

    <!-- Chart.js库 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/4.4.0/chart.min.js" 
            onerror="this.onerror=null; this.src='https://unpkg.com/chart.js@4.4.0/dist/chart.min.js'"></script>

    <script>
        let charts = {};
        let logElement = document.getElementById('systemLog');
        let statusElement = document.getElementById('loadingStatus');

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            logElement.textContent += logEntry;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(logEntry);
        }

        function updateStatus(message, type) {
            statusElement.textContent = message;
            statusElement.className = `status ${type}`;
        }

        function testChartJS() {
            log('测试Chart.js库状态...', 'info');
            
            if (typeof Chart !== 'undefined') {
                log(`✓ Chart.js库已加载，版本: ${Chart.version}`, 'success');
                updateStatus('Chart.js库正常', 'success');
                return true;
            } else {
                log('✗ Chart.js库未加载', 'error');
                updateStatus('Chart.js库未加载', 'error');
                return false;
            }
        }

        function generateSampleData(type = 'sine', points = 100) {
            const data = [];
            for (let i = 0; i < points; i++) {
                const t = i / points;
                let value;
                
                switch (type) {
                    case 'sine':
                        value = Math.sin(2 * Math.PI * 5 * t);
                        break;
                    case 'cosine':
                        value = Math.cos(2 * Math.PI * 3 * t);
                        break;
                    case 'square':
                        value = Math.sign(Math.sin(2 * Math.PI * 2 * t));
                        break;
                    default:
                        value = Math.random() * 2 - 1;
                }
                
                data.push({ x: t, y: value });
            }
            return data;
        }

        function createTestCharts() {
            if (!testChartJS()) {
                log('Chart.js未加载，无法创建图表', 'error');
                return;
            }

            try {
                log('开始创建测试图表...', 'info');

                // 生成测试数据
                const timeData = generateSampleData('sine', 200);
                const freqData = generateSampleData('cosine', 50);

                // 创建时域图表
                const timeCanvas = document.getElementById('timeChart');
                const timeCtx = timeCanvas.getContext('2d');
                
                if (charts.timeChart) {
                    charts.timeChart.destroy();
                }

                charts.timeChart = new Chart(timeCtx, {
                    type: 'line',
                    data: {
                        datasets: [{
                            label: '时域信号',
                            data: timeData,
                            borderColor: '#3b82f6',
                            backgroundColor: 'rgba(59, 130, 246, 0.1)',
                            borderWidth: 2,
                            pointRadius: 0,
                            fill: true
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            x: {
                                type: 'linear',
                                title: { display: true, text: '时间 (s)' }
                            },
                            y: {
                                title: { display: true, text: '幅度' }
                            }
                        },
                        plugins: {
                            legend: { display: true }
                        }
                    }
                });

                // 创建频域图表
                const freqCanvas = document.getElementById('frequencyChart');
                const freqCtx = freqCanvas.getContext('2d');
                
                if (charts.frequencyChart) {
                    charts.frequencyChart.destroy();
                }

                charts.frequencyChart = new Chart(freqCtx, {
                    type: 'line',
                    data: {
                        datasets: [{
                            label: '频域信号',
                            data: freqData,
                            borderColor: '#ef4444',
                            backgroundColor: 'rgba(239, 68, 68, 0.1)',
                            borderWidth: 2,
                            pointRadius: 0,
                            fill: true
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            x: {
                                type: 'linear',
                                title: { display: true, text: '频率 (Hz)' }
                            },
                            y: {
                                title: { display: true, text: '幅度' }
                            }
                        },
                        plugins: {
                            legend: { display: true }
                        }
                    }
                });

                log('✓ 测试图表创建成功', 'success');
                updateStatus('图表创建成功', 'success');

            } catch (error) {
                log(`✗ 图表创建失败: ${error.message}`, 'error');
                updateStatus('图表创建失败', 'error');
            }
        }

        function loadSampleData() {
            log('加载示例数据...', 'info');
            createTestCharts();
        }

        function clearAll() {
            log('清空所有图表...', 'info');
            
            Object.keys(charts).forEach(key => {
                if (charts[key] && charts[key].destroy) {
                    charts[key].destroy();
                }
            });
            charts = {};
            
            log('✓ 所有图表已清空', 'success');
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('页面DOM加载完成', 'info');
            updateStatus('页面已加载', 'success');
            
            // 延迟测试Chart.js
            setTimeout(() => {
                testChartJS();
                log('初始化完成，可以开始测试', 'success');
            }, 1000);
        });

        // 错误捕获
        window.addEventListener('error', function(event) {
            log(`JavaScript错误: ${event.error.message} 在 ${event.filename}:${event.lineno}`, 'error');
        });

        // Chart.js加载检测
        window.addEventListener('load', function() {
            setTimeout(() => {
                if (typeof Chart !== 'undefined') {
                    log('Chart.js库在页面加载后可用', 'success');
                } else {
                    log('Chart.js库在页面加载后仍不可用', 'error');
                }
            }, 2000);
        });
    </script>
</body>
</html>
