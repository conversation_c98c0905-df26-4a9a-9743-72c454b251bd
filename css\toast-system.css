/* Toast通知系统样式 - 美观版本 */

.toast-container {
    position: fixed;
    top: 24px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 10000;
    display: flex;
    flex-direction: column;
    gap: 12px;
    pointer-events: none;
    width: 100%;
    max-width: 420px;
    padding: 0 20px;
    box-sizing: border-box;
}

.toast {
    background: linear-gradient(145deg,
        rgba(255, 255, 255, 0.98) 0%,
        rgba(255, 255, 255, 0.96) 50%,
        rgba(255, 255, 255, 0.98) 100%);
    border-radius: 24px;
    box-shadow:
        0 25px 50px rgba(0, 0, 0, 0.08),
        0 15px 25px rgba(0, 0, 0, 0.06),
        0 8px 16px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.95),
        inset 0 -1px 0 rgba(0, 0, 0, 0.02);
    padding: 20px 24px 24px 24px;
    display: flex;
    flex-direction: column;
    transform: translateY(-60px) scale(0.96);
    opacity: 0;
    transition: all 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.1);
    pointer-events: auto;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(30px);
    -webkit-backdrop-filter: blur(30px);
    border: 1px solid rgba(255, 255, 255, 0.4);
    min-width: 340px;
    max-width: 400px;
}

.toast.show {
    transform: translateY(0) scale(1);
    opacity: 1;
}

/* Toast类型样式 - 美观版本 */
.toast-success {
    border-left: 5px solid #00d97e;
    border-top-left-radius: 24px;
    border-bottom-left-radius: 24px;
    background: linear-gradient(145deg,
        rgba(0, 217, 126, 0.06) 0%,
        rgba(255, 255, 255, 0.98) 25%,
        rgba(255, 255, 255, 0.96) 75%,
        rgba(0, 217, 126, 0.04) 100%);
    box-shadow:
        0 25px 50px rgba(0, 217, 126, 0.12),
        0 15px 25px rgba(0, 0, 0, 0.06),
        0 8px 16px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.95);
}

.toast-error {
    border-left: 5px solid #e63757;
    border-top-left-radius: 24px;
    border-bottom-left-radius: 24px;
    background: linear-gradient(145deg,
        rgba(230, 55, 87, 0.06) 0%,
        rgba(255, 255, 255, 0.98) 25%,
        rgba(255, 255, 255, 0.96) 75%,
        rgba(230, 55, 87, 0.04) 100%);
    box-shadow:
        0 25px 50px rgba(230, 55, 87, 0.12),
        0 15px 25px rgba(0, 0, 0, 0.06),
        0 8px 16px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.95);
}

.toast-info {
    border-left: 5px solid #2c7be5;
    border-top-left-radius: 24px;
    border-bottom-left-radius: 24px;
    background: linear-gradient(145deg,
        rgba(44, 123, 229, 0.06) 0%,
        rgba(255, 255, 255, 0.98) 25%,
        rgba(255, 255, 255, 0.96) 75%,
        rgba(44, 123, 229, 0.04) 100%);
    box-shadow:
        0 25px 50px rgba(44, 123, 229, 0.12),
        0 15px 25px rgba(0, 0, 0, 0.06),
        0 8px 16px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.95);
}

.toast-warning {
    border-left: 5px solid #f6c343;
    border-top-left-radius: 24px;
    border-bottom-left-radius: 24px;
    background: linear-gradient(145deg,
        rgba(246, 195, 67, 0.06) 0%,
        rgba(255, 255, 255, 0.98) 25%,
        rgba(255, 255, 255, 0.96) 75%,
        rgba(246, 195, 67, 0.04) 100%);
    box-shadow:
        0 25px 50px rgba(246, 195, 67, 0.12),
        0 15px 25px rgba(0, 0, 0, 0.06),
        0 8px 16px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.95);
}

/* Toast内容 - 美观版本 */
.toast-content {
    flex: 1;
    padding-bottom: 8px;
}

.toast-title {
    font-size: 16px;
    font-weight: 700;
    color: #1a202c;
    margin-bottom: 6px;
    line-height: 1.3;
    letter-spacing: -0.02em;
    font-family: 'Nunito', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

.toast-message {
    font-size: 14px;
    color: #4a5568;
    line-height: 1.5;
    margin: 0;
    font-weight: 400;
    font-family: 'Nunito', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

/* Toast进度条 - 美观版本 */
.toast-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 6px;
    background: rgba(0, 0, 0, 0.04);
    overflow: hidden;
    border-radius: 0 0 24px 24px;
}

.toast-progress-bar {
    height: 100%;
    width: 100%;
    transition: width linear;
    border-radius: 0 0 24px 24px;
    position: relative;
    background: linear-gradient(90deg,
        rgba(255, 255, 255, 0.15) 0%,
        rgba(255, 255, 255, 0.35) 50%,
        rgba(255, 255, 255, 0.15) 100%);
}

.toast-progress-bar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: inherit;
    border-radius: inherit;
}

.toast-progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 30%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(255, 255, 255, 0.6),
        transparent);
    animation: progressShimmer 2s ease-in-out infinite;
    border-radius: inherit;
}

@keyframes progressShimmer {
    0% {
        transform: translateX(-100%);
        opacity: 0;
    }
    50% {
        opacity: 1;
    }
    100% {
        transform: translateX(400%);
        opacity: 0;
    }
}

.toast-success .toast-progress-bar::before {
    background: linear-gradient(90deg, #00d97e, #00b86b);
    box-shadow: 0 0 8px rgba(0, 217, 126, 0.4);
}

.toast-error .toast-progress-bar::before {
    background: linear-gradient(90deg, #e63757, #d63447);
    box-shadow: 0 0 8px rgba(230, 55, 87, 0.4);
}

.toast-info .toast-progress-bar::before {
    background: linear-gradient(90deg, #2c7be5, #1a68d1);
    box-shadow: 0 0 8px rgba(44, 123, 229, 0.4);
}

.toast-warning .toast-progress-bar::before {
    background: linear-gradient(90deg, #f6c343, #f1b434);
    box-shadow: 0 0 8px rgba(246, 195, 67, 0.4);
}

/* Toast动画效果 */
.toast::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.6s ease;
}

.toast.show::before {
    left: 100%;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .toast-container {
        top: 10px;
        padding: 0 15px;
        max-width: 100%;
    }
    
    .toast {
        padding: 14px 16px;
        border-radius: 10px;
    }
    
    .toast-title {
        font-size: 15px;
    }
    
    .toast-message {
        font-size: 13px;
    }
}

@media (max-width: 480px) {
    .toast-container {
        top: 5px;
        padding: 0 10px;
    }
    
    .toast {
        padding: 12px 14px;
        gap: 10px;
    }
    
    .toast-title {
        font-size: 14px;
    }
    
    .toast-message {
        font-size: 12px;
    }
}

/* 多个Toast的堆叠效果 */
.toast:nth-child(1) {
    z-index: 100;
}

.toast:nth-child(2) {
    z-index: 99;
    transform: translateY(-100px) scale(0.98);
}

.toast:nth-child(3) {
    z-index: 98;
    transform: translateY(-100px) scale(0.96);
}

.toast:nth-child(2).show {
    transform: translateY(0) scale(0.98);
}

.toast:nth-child(3).show {
    transform: translateY(0) scale(0.96);
}

/* Toast关闭动画 */
.toast.hide {
    transform: translateY(-100px) scale(0.9);
    opacity: 0;
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
    .toast {
        background: #1e293b;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    }
    
    .toast.success {
        background: linear-gradient(135deg, rgba(0, 217, 126, 0.1) 0%, rgba(30, 41, 59, 1) 100%);
    }
    
    .toast.error {
        background: linear-gradient(135deg, rgba(230, 55, 87, 0.1) 0%, rgba(30, 41, 59, 1) 100%);
    }
    
    .toast.info {
        background: linear-gradient(135deg, rgba(44, 123, 229, 0.1) 0%, rgba(30, 41, 59, 1) 100%);
    }
    
    .toast.warning {
        background: linear-gradient(135deg, rgba(246, 195, 67, 0.1) 0%, rgba(30, 41, 59, 1) 100%);
    }
    
    .toast-title {
        color: #f1f5f9;
    }
    
    .toast-message {
        color: #94a3b8;
    }
    
    .toast-progress {
        background: rgba(255, 255, 255, 0.1);
    }
}

/* 特殊效果 */
.toast.success::after {
    content: '';
    position: absolute;
    top: 50%;
    right: 20px;
    transform: translateY(-50%);
    width: 20px;
    height: 20px;
    background: #00d97e;
    border-radius: 50%;
    opacity: 0.1;
    animation: pulse 2s infinite;
}

.toast.error::after {
    content: '';
    position: absolute;
    top: 50%;
    right: 20px;
    transform: translateY(-50%);
    width: 20px;
    height: 20px;
    background: #e63757;
    border-radius: 50%;
    opacity: 0.1;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: translateY(-50%) scale(1);
        opacity: 0.1;
    }
    50% {
        transform: translateY(-50%) scale(1.2);
        opacity: 0.2;
    }
    100% {
        transform: translateY(-50%) scale(1);
        opacity: 0.1;
    }
}
