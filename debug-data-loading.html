<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Data页面加载调试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .debug-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .test-frame {
            width: 100%;
            height: 600px;
            border: 2px solid #ddd;
            border-radius: 5px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1>Data页面加载调试工具</h1>
        
        <div class="status warning" id="loadStatus">准备测试...</div>
        
        <button onclick="testDataPageLoading()">测试data.html加载</button>
        <button onclick="clearLog()">清空日志</button>
        <button onclick="openDataPageInNewTab()">在新标签页打开data.html</button>
        
        <h3>加载日志</h3>
        <div id="debugLog" class="log">等待测试...</div>
        
        <h3>页面预览 (iframe)</h3>
        <iframe id="dataFrame" class="test-frame" src="about:blank"></iframe>
    </div>

    <script>
        let logElement = document.getElementById('debugLog');
        let statusElement = document.getElementById('loadStatus');

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            logElement.textContent += logEntry;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(logEntry);
        }

        function clearLog() {
            logElement.textContent = '';
        }

        function updateStatus(message, type) {
            statusElement.textContent = message;
            statusElement.className = `status ${type}`;
        }

        function testDataPageLoading() {
            log('开始测试data.html页面加载...', 'info');
            updateStatus('正在测试...', 'warning');
            
            const iframe = document.getElementById('dataFrame');
            
            // 设置超时检测
            const loadTimeout = setTimeout(() => {
                log('页面加载超时 (10秒)', 'error');
                updateStatus('页面加载超时', 'error');
            }, 10000);
            
            // 监听iframe加载事件
            iframe.onload = function() {
                clearTimeout(loadTimeout);
                log('iframe加载完成', 'success');
                
                try {
                    // 检查iframe内容
                    const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                    
                    if (iframeDoc) {
                        log('可以访问iframe文档', 'success');
                        
                        // 检查加载动画是否存在
                        const loader = iframeDoc.getElementById('pageLoader');
                        if (loader) {
                            log('找到页面加载器元素', 'info');
                            
                            // 监听加载器状态变化
                            const observer = new MutationObserver(function(mutations) {
                                mutations.forEach(function(mutation) {
                                    if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                                        if (loader.classList.contains('loaded')) {
                                            log('加载动画已隐藏，页面加载完成', 'success');
                                            updateStatus('页面加载成功', 'success');
                                        }
                                    }
                                });
                            });
                            
                            observer.observe(loader, { attributes: true });
                            
                            // 检查加载器当前状态
                            if (loader.classList.contains('loaded')) {
                                log('加载动画已经隐藏', 'success');
                                updateStatus('页面已加载完成', 'success');
                            } else {
                                log('加载动画仍在显示，等待完成...', 'warning');
                                updateStatus('等待页面加载完成...', 'warning');
                                
                                // 设置额外的超时检测
                                setTimeout(() => {
                                    if (!loader.classList.contains('loaded')) {
                                        log('页面可能卡在加载动画', 'error');
                                        updateStatus('页面卡在加载中', 'error');
                                        
                                        // 检查控制台错误
                                        try {
                                            const iframeWindow = iframe.contentWindow;
                                            if (iframeWindow && iframeWindow.console) {
                                                log('尝试访问iframe控制台...', 'info');
                                            }
                                        } catch (e) {
                                            log('无法访问iframe控制台: ' + e.message, 'warning');
                                        }
                                    }
                                }, 8000);
                            }
                        } else {
                            log('未找到页面加载器元素', 'warning');
                        }
                        
                        // 检查Chart.js
                        setTimeout(() => {
                            try {
                                const iframeWindow = iframe.contentWindow;
                                if (iframeWindow.Chart) {
                                    log('Chart.js已在iframe中加载', 'success');
                                } else {
                                    log('Chart.js未在iframe中加载', 'error');
                                }
                            } catch (e) {
                                log('检查Chart.js时出错: ' + e.message, 'warning');
                            }
                        }, 2000);
                        
                    } else {
                        log('无法访问iframe文档', 'error');
                        updateStatus('无法访问页面内容', 'error');
                    }
                } catch (error) {
                    log('检查iframe内容时出错: ' + error.message, 'error');
                    updateStatus('页面检查失败', 'error');
                }
            };
            
            iframe.onerror = function() {
                clearTimeout(loadTimeout);
                log('iframe加载失败', 'error');
                updateStatus('页面加载失败', 'error');
            };
            
            // 开始加载页面
            log('开始加载data.html到iframe...', 'info');
            iframe.src = 'data.html';
        }

        function openDataPageInNewTab() {
            log('在新标签页打开data.html', 'info');
            window.open('data.html', '_blank');
        }

        // 页面加载时自动运行测试
        window.addEventListener('load', function() {
            log('调试工具已加载', 'info');
        });

        // 捕获所有错误
        window.addEventListener('error', function(event) {
            log(`JavaScript错误: ${event.error.message} 在 ${event.filename}:${event.lineno}`, 'error');
        });
    </script>
</body>
</html>
