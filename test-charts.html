<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chart.js 测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-section h3 {
            color: #333;
            margin-bottom: 15px;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .chart-container {
            width: 100%;
            height: 300px;
            margin: 20px 0;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 10px;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Chart.js 库测试工具</h1>
        
        <div class="test-section">
            <h3>1. Chart.js 库加载状态</h3>
            <div id="chartjsStatus" class="status warning">正在检测...</div>
            <button onclick="testChartJS()">重新检测</button>
            <button onclick="loadFallbackChartJS()">加载备用Chart.js</button>
        </div>

        <div class="test-section">
            <h3>2. CDN 连接测试</h3>
            <div id="cdnStatus" class="status warning">准备测试</div>
            <button onclick="testCDNConnections()">测试CDN连接</button>
        </div>

        <div class="test-section">
            <h3>3. 图表创建测试</h3>
            <div id="chartCreationStatus" class="status warning">准备测试</div>
            <button onclick="testChartCreation()">测试创建图表</button>
            <button onclick="clearCharts()">清空图表</button>
            
            <div class="grid">
                <div class="chart-container">
                    <h4>时域波形图测试</h4>
                    <canvas id="testTimeChart" width="400" height="200"></canvas>
                </div>
                <div class="chart-container">
                    <h4>频域分析图测试</h4>
                    <canvas id="testFrequencyChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>4. 控制台日志</h3>
            <div id="consoleLog" class="log">等待日志...</div>
            <button onclick="clearLog()">清空日志</button>
        </div>
    </div>

    <!-- Chart.js 库 - 多重CDN备用方案 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/4.4.0/chart.min.js" 
            onerror="this.onerror=null; this.src='https://unpkg.com/chart.js@4.4.0/dist/chart.min.js'"></script>

    <script>
        let testCharts = {};

        // 日志记录函数
        function log(message, type = 'info') {
            const logElement = document.getElementById('consoleLog');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            logElement.textContent += logEntry;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(logEntry);
        }

        function clearLog() {
            document.getElementById('consoleLog').textContent = '';
        }

        // 测试 Chart.js 库
        function testChartJS() {
            const statusElement = document.getElementById('chartjsStatus');
            
            if (typeof Chart !== 'undefined') {
                statusElement.className = 'status success';
                statusElement.textContent = '✓ Chart.js 库已成功加载 - 版本: ' + Chart.version;
                log('Chart.js 库检测成功，版本: ' + Chart.version, 'success');
                return true;
            } else {
                statusElement.className = 'status error';
                statusElement.textContent = '✗ Chart.js 库未加载';
                log('Chart.js 库未加载', 'error');
                return false;
            }
        }

        // 加载备用Chart.js
        function loadFallbackChartJS() {
            log('尝试加载备用Chart.js...', 'info');
            
            const script = document.createElement('script');
            script.src = 'https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js';
            script.onload = function() {
                log('备用Chart.js加载成功', 'success');
                testChartJS();
            };
            script.onerror = function() {
                log('备用Chart.js加载失败', 'error');
            };
            document.head.appendChild(script);
        }

        // 测试CDN连接
        function testCDNConnections() {
            const statusElement = document.getElementById('cdnStatus');
            const cdnUrls = [
                'https://cdnjs.cloudflare.com/ajax/libs/Chart.js/4.4.0/chart.min.js',
                'https://unpkg.com/chart.js@4.4.0/dist/chart.min.js',
                'https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js'
            ];

            statusElement.className = 'status warning';
            statusElement.textContent = '正在测试CDN连接...';
            log('开始测试Chart.js CDN连接...', 'info');

            Promise.allSettled(
                cdnUrls.map(url => 
                    fetch(url, { method: 'HEAD', mode: 'no-cors' })
                        .then(() => ({ url, status: 'success' }))
                        .catch(error => ({ url, status: 'failed', error: error.message }))
                )
            ).then(results => {
                let successCount = 0;
                results.forEach(result => {
                    if (result.status === 'fulfilled') {
                        successCount++;
                        log(`CDN连接成功: ${result.value.url}`, 'success');
                    } else {
                        log(`CDN连接失败: ${result.reason}`, 'error');
                    }
                });

                if (successCount > 0) {
                    statusElement.className = 'status success';
                    statusElement.textContent = `✓ ${successCount}/${cdnUrls.length} CDN连接成功`;
                } else {
                    statusElement.className = 'status error';
                    statusElement.textContent = '✗ 所有CDN连接失败';
                }
            });
        }

        // 测试图表创建
        function testChartCreation() {
            const statusElement = document.getElementById('chartCreationStatus');
            
            if (!testChartJS()) {
                statusElement.className = 'status error';
                statusElement.textContent = '✗ Chart.js未加载，无法创建图表';
                return;
            }

            try {
                // 创建测试数据
                const timeData = [];
                const frequencyData = [];
                
                for (let i = 0; i < 100; i++) {
                    const t = i / 100;
                    const value = Math.sin(2 * Math.PI * 5 * t) + 0.5 * Math.sin(2 * Math.PI * 10 * t);
                    timeData.push({ x: t, y: value });
                    
                    // 简单的频域数据模拟
                    if (i < 50) {
                        frequencyData.push({ x: i, y: Math.abs(Math.sin(i / 10)) });
                    }
                }

                // 创建时域图表
                const timeCanvas = document.getElementById('testTimeChart');
                const timeCtx = timeCanvas.getContext('2d');
                
                if (testCharts.timeChart) {
                    testCharts.timeChart.destroy();
                }

                testCharts.timeChart = new Chart(timeCtx, {
                    type: 'line',
                    data: {
                        datasets: [{
                            label: '测试信号',
                            data: timeData,
                            borderColor: '#3b82f6',
                            backgroundColor: 'rgba(59, 130, 246, 0.1)',
                            borderWidth: 2,
                            pointRadius: 0
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            x: {
                                type: 'linear',
                                title: { display: true, text: '时间 (s)' }
                            },
                            y: {
                                title: { display: true, text: '幅度' }
                            }
                        }
                    }
                });

                // 创建频域图表
                const freqCanvas = document.getElementById('testFrequencyChart');
                const freqCtx = freqCanvas.getContext('2d');
                
                if (testCharts.frequencyChart) {
                    testCharts.frequencyChart.destroy();
                }

                testCharts.frequencyChart = new Chart(freqCtx, {
                    type: 'line',
                    data: {
                        datasets: [{
                            label: '频谱',
                            data: frequencyData,
                            borderColor: '#ef4444',
                            backgroundColor: 'rgba(239, 68, 68, 0.1)',
                            borderWidth: 2,
                            pointRadius: 0
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            x: {
                                type: 'linear',
                                title: { display: true, text: '频率 (Hz)' }
                            },
                            y: {
                                title: { display: true, text: '幅度' }
                            }
                        }
                    }
                });

                statusElement.className = 'status success';
                statusElement.textContent = '✓ 图表创建成功';
                log('测试图表创建成功', 'success');

            } catch (error) {
                statusElement.className = 'status error';
                statusElement.textContent = '✗ 图表创建失败: ' + error.message;
                log('图表创建失败: ' + error.message, 'error');
            }
        }

        // 清空图表
        function clearCharts() {
            Object.keys(testCharts).forEach(key => {
                if (testCharts[key] && testCharts[key].destroy) {
                    testCharts[key].destroy();
                }
            });
            testCharts = {};
            log('所有测试图表已清空', 'info');
        }

        // 页面加载时自动运行测试
        window.addEventListener('load', function() {
            log('页面加载完成，开始自动诊断...', 'info');
            setTimeout(testChartJS, 500);
            setTimeout(testCDNConnections, 1000);
        });

        // 捕获所有错误
        window.addEventListener('error', function(event) {
            log(`JavaScript错误: ${event.error.message} 在 ${event.filename}:${event.lineno}`, 'error');
        });
    </script>
</body>
</html>
