// Particles.js 简化备用实现
// 当原始particles.js库无法加载时使用

(function() {
    'use strict';
    
    // 如果particles.js已经加载，则不需要备用实现
    if (typeof window.particlesJS !== 'undefined') {
        console.log('particles.js已加载，跳过备用实现');
        return;
    }
    
    console.log('加载particles.js备用实现...');
    
    // 简化的particlesJS函数
    window.particlesJS = function(elementId, config) {
        console.log('使用备用particles.js实现');
        
        const container = document.getElementById(elementId);
        if (!container) {
            console.warn('找不到particles容器:', elementId);
            return;
        }
        
        // 创建canvas元素
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        
        // 设置canvas样式
        canvas.style.position = 'absolute';
        canvas.style.top = '0';
        canvas.style.left = '0';
        canvas.style.width = '100%';
        canvas.style.height = '100%';
        canvas.style.pointerEvents = 'none';
        canvas.style.zIndex = '-1';
        
        container.appendChild(canvas);
        
        // 粒子数组
        const particles = [];
        const particleCount = config.particles?.number?.value || 50;
        
        // 调整canvas大小
        function resizeCanvas() {
            canvas.width = container.offsetWidth;
            canvas.height = container.offsetHeight;
        }
        
        // 粒子类
        class Particle {
            constructor() {
                this.reset();
                this.y = Math.random() * canvas.height;
            }
            
            reset() {
                this.x = Math.random() * canvas.width;
                this.y = -10;
                this.vx = (Math.random() - 0.5) * 2;
                this.vy = Math.random() * 2 + 1;
                this.size = Math.random() * 3 + 1;
                this.opacity = Math.random() * 0.5 + 0.2;
                this.life = 1;
            }
            
            update() {
                this.x += this.vx;
                this.y += this.vy;
                this.life -= 0.001;
                
                // 重置粒子位置
                if (this.y > canvas.height + 10 || this.life <= 0) {
                    this.reset();
                }
                
                // 边界检查
                if (this.x < 0 || this.x > canvas.width) {
                    this.vx *= -1;
                }
            }
            
            draw() {
                ctx.save();
                ctx.globalAlpha = this.opacity * this.life;
                ctx.fillStyle = config.particles?.color?.value || '#2c7be5';
                ctx.beginPath();
                ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
                ctx.fill();
                ctx.restore();
            }
        }
        
        // 创建粒子
        for (let i = 0; i < particleCount; i++) {
            particles.push(new Particle());
        }
        
        // 绘制连线
        function drawLines() {
            if (!config.particles?.line_linked?.enable) return;
            
            const maxDistance = config.particles.line_linked.distance || 150;
            const lineOpacity = config.particles.line_linked.opacity || 0.3;
            const lineColor = config.particles.line_linked.color || '#5499ff';
            
            ctx.strokeStyle = lineColor;
            ctx.lineWidth = config.particles.line_linked.width || 1;
            
            for (let i = 0; i < particles.length; i++) {
                for (let j = i + 1; j < particles.length; j++) {
                    const dx = particles[i].x - particles[j].x;
                    const dy = particles[i].y - particles[j].y;
                    const distance = Math.sqrt(dx * dx + dy * dy);
                    
                    if (distance < maxDistance) {
                        ctx.save();
                        ctx.globalAlpha = lineOpacity * (1 - distance / maxDistance);
                        ctx.beginPath();
                        ctx.moveTo(particles[i].x, particles[i].y);
                        ctx.lineTo(particles[j].x, particles[j].y);
                        ctx.stroke();
                        ctx.restore();
                    }
                }
            }
        }
        
        // 动画循环
        function animate() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 更新和绘制粒子
            particles.forEach(particle => {
                particle.update();
                particle.draw();
            });
            
            // 绘制连线
            drawLines();
            
            requestAnimationFrame(animate);
        }
        
        // 鼠标交互
        if (config.interactivity?.events?.onhover?.enable) {
            container.addEventListener('mousemove', function(e) {
                const rect = container.getBoundingClientRect();
                const mouseX = e.clientX - rect.left;
                const mouseY = e.clientY - rect.top;
                
                particles.forEach(particle => {
                    const dx = mouseX - particle.x;
                    const dy = mouseY - particle.y;
                    const distance = Math.sqrt(dx * dx + dy * dy);
                    
                    if (distance < 100) {
                        particle.vx += dx * 0.0001;
                        particle.vy += dy * 0.0001;
                    }
                });
            });
        }
        
        // 点击交互
        if (config.interactivity?.events?.onclick?.enable) {
            container.addEventListener('click', function(e) {
                const rect = container.getBoundingClientRect();
                const mouseX = e.clientX - rect.left;
                const mouseY = e.clientY - rect.top;
                
                // 添加新粒子
                for (let i = 0; i < 4; i++) {
                    const particle = new Particle();
                    particle.x = mouseX + (Math.random() - 0.5) * 20;
                    particle.y = mouseY + (Math.random() - 0.5) * 20;
                    particles.push(particle);
                }
                
                // 限制粒子数量
                if (particles.length > particleCount * 2) {
                    particles.splice(0, particles.length - particleCount);
                }
            });
        }
        
        // 窗口大小改变时重新调整
        window.addEventListener('resize', resizeCanvas);
        
        // 初始化
        resizeCanvas();
        animate();
        
        console.log('备用particles.js实现初始化完成');
        
        return {
            destroy: function() {
                container.removeChild(canvas);
                particles.length = 0;
            }
        };
    };
    
    // 标记为备用实现
    window.particlesJS.isFallback = true;
    
    console.log('particles.js备用实现加载完成');
})();
