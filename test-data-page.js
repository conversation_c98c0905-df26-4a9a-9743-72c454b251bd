// 测试data.html页面的Chart.js功能
console.log('开始测试data.html页面的Chart.js功能...');

// 等待页面完全加载
setTimeout(() => {
    console.log('=== Chart.js 库状态检查 ===');
    
    // 检查Chart.js是否加载
    if (typeof Chart !== 'undefined') {
        console.log('✓ Chart.js库已成功加载，版本:', Chart.version);
    } else {
        console.error('✗ Chart.js库未加载');
        return;
    }
    
    // 检查图表画布元素
    console.log('\n=== 图表画布元素检查 ===');
    const canvasElements = [
        'timeChartCanvas',
        'frequencyChartCanvas', 
        'distributionChartCanvas',
        'polarChartCanvas',
        'vectorChartCanvas'
    ];
    
    canvasElements.forEach(canvasId => {
        const canvas = document.getElementById(canvasId);
        if (canvas) {
            console.log(`✓ 找到画布元素: ${canvasId}`);
        } else {
            console.error(`✗ 未找到画布元素: ${canvasId}`);
        }
    });
    
    // 检查图表对象
    console.log('\n=== 图表对象检查 ===');
    if (typeof charts !== 'undefined') {
        console.log('✓ charts对象存在');
        Object.keys(charts).forEach(chartKey => {
            if (charts[chartKey]) {
                console.log(`✓ 图表已创建: ${chartKey}`);
            } else {
                console.log(`- 图表未创建: ${chartKey}`);
            }
        });
    } else {
        console.error('✗ charts对象不存在');
    }
    
    // 测试图表更新函数
    console.log('\n=== 图表函数检查 ===');
    const chartFunctions = [
        'updateTimeChart',
        'updateFrequencyChart',
        'updateDistributionChart',
        'updatePolarChart',
        'updateVectorChart'
    ];
    
    chartFunctions.forEach(funcName => {
        if (typeof window[funcName] === 'function') {
            console.log(`✓ 函数存在: ${funcName}`);
        } else {
            console.error(`✗ 函数不存在: ${funcName}`);
        }
    });
    
    // 测试默认数据加载
    console.log('\n=== 默认数据加载测试 ===');
    if (typeof loadDefaultData === 'function') {
        console.log('✓ loadDefaultData函数存在，尝试调用...');
        try {
            loadDefaultData();
            console.log('✓ loadDefaultData调用成功');
        } catch (error) {
            console.error('✗ loadDefaultData调用失败:', error.message);
        }
    } else {
        console.error('✗ loadDefaultData函数不存在');
    }
    
    // 监听错误
    console.log('\n=== 错误监听已启用 ===');
    window.addEventListener('error', (event) => {
        console.error('页面错误:', event.error.message, '在', event.filename + ':' + event.lineno);
    });
    
    console.log('\n=== 测试完成 ===');
    console.log('请查看上述输出以确认Chart.js功能是否正常');
    
}, 2000); // 等待2秒确保页面完全加载
