// 登录页面调试脚本
// 在浏览器控制台中运行此脚本来诊断问题

console.log('=== 登录页面调试工具 ===');

// 1. 检查particles.js库
function checkParticlesJS() {
    console.log('\n1. 检查particles.js库状态:');
    
    if (typeof particlesJS !== 'undefined') {
        console.log('✓ particles.js库已加载');
        return true;
    } else {
        console.log('✗ particles.js库未加载');
        
        // 检查script标签
        const particlesScripts = document.querySelectorAll('script[src*="particles"]');
        console.log(`找到${particlesScripts.length}个particles相关的script标签:`);
        particlesScripts.forEach((script, index) => {
            console.log(`  ${index + 1}. ${script.src}`);
            console.log(`     加载状态: ${script.readyState || '未知'}`);
        });
        
        return false;
    }
}

// 2. 检查页面元素
function checkPageElements() {
    console.log('\n2. 检查页面关键元素:');
    
    const elements = {
        'particles容器': '#particles-js',
        '登录表单': '#loginForm',
        '用户名输入': '#username',
        '密码输入': '#password',
        '登录按钮': '.login-btn'
    };
    
    Object.entries(elements).forEach(([name, selector]) => {
        const element = document.querySelector(selector);
        if (element) {
            console.log(`✓ ${name}: 存在`);
        } else {
            console.log(`✗ ${name}: 不存在`);
        }
    });
}

// 3. 检查网络连接
async function checkNetworkConnections() {
    console.log('\n3. 检查CDN连接状态:');
    
    const cdnUrls = [
        'https://cdnjs.cloudflare.com/ajax/libs/particles.js/2.0.0/particles.min.js',
        'https://unpkg.com/particles.js@2.0.0/particles.min.js',
        'https://cdn.jsdelivr.net/particles.js/2.0.0/particles.min.js'
    ];
    
    for (const url of cdnUrls) {
        try {
            const response = await fetch(url, { method: 'HEAD', mode: 'no-cors' });
            console.log(`✓ ${url}: 可访问`);
        } catch (error) {
            console.log(`✗ ${url}: 无法访问 (${error.message})`);
        }
    }
}

// 4. 检查目标页面
async function checkTargetPage() {
    console.log('\n4. 检查目标页面data.html:');
    
    try {
        const response = await fetch('data.html', { method: 'HEAD' });
        if (response.ok) {
            console.log('✓ data.html: 存在且可访问');
        } else {
            console.log(`✗ data.html: 存在但返回状态码 ${response.status}`);
        }
    } catch (error) {
        console.log(`✗ data.html: 无法访问 (${error.message})`);
    }
}

// 5. 模拟登录流程
function simulateLogin() {
    console.log('\n5. 模拟登录流程:');
    
    const usernameInput = document.querySelector('#username');
    const passwordInput = document.querySelector('#password');
    const loginForm = document.querySelector('#loginForm');
    
    if (!usernameInput || !passwordInput || !loginForm) {
        console.log('✗ 无法找到登录表单元素');
        return;
    }
    
    // 填入测试数据
    usernameInput.value = 'test';
    passwordInput.value = 'test123';
    
    console.log('✓ 已填入测试用户名和密码');
    console.log('提示: 可以手动点击登录按钮测试登录流程');
}

// 6. 启用备用粒子效果
function enableParticlesFallback() {
    console.log('\n6. 启用备用粒子效果:');
    
    document.body.classList.add('particles-fallback');
    const particlesContainer = document.querySelector('#particles-js');
    if (particlesContainer) {
        particlesContainer.style.display = 'none';
    }
    
    console.log('✓ 已启用CSS备用粒子效果');
}

// 7. 修复常见问题
function fixCommonIssues() {
    console.log('\n7. 尝试修复常见问题:');
    
    // 如果particles.js未加载，启用备用方案
    if (!checkParticlesJS()) {
        enableParticlesFallback();
    }
    
    // 确保页面加载动画正常结束
    const pageLoader = document.querySelector('.page-loader');
    if (pageLoader && !pageLoader.classList.contains('loaded')) {
        pageLoader.classList.add('loaded');
        console.log('✓ 已手动结束页面加载动画');
    }
    
    console.log('✓ 常见问题修复完成');
}

// 运行完整诊断
async function runFullDiagnosis() {
    console.log('开始运行完整诊断...\n');
    
    checkParticlesJS();
    checkPageElements();
    await checkNetworkConnections();
    await checkTargetPage();
    simulateLogin();
    fixCommonIssues();
    
    console.log('\n=== 诊断完成 ===');
    console.log('如果仍有问题，请检查浏览器控制台的错误信息');
}

// 导出函数供手动调用
window.debugLogin = {
    checkParticlesJS,
    checkPageElements,
    checkNetworkConnections,
    checkTargetPage,
    simulateLogin,
    enableParticlesFallback,
    fixCommonIssues,
    runFullDiagnosis
};

// 自动运行诊断
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', runFullDiagnosis);
} else {
    runFullDiagnosis();
}

console.log('\n可用的调试命令:');
console.log('debugLogin.runFullDiagnosis() - 运行完整诊断');
console.log('debugLogin.checkParticlesJS() - 检查particles.js');
console.log('debugLogin.enableParticlesFallback() - 启用备用粒子效果');
console.log('debugLogin.fixCommonIssues() - 修复常见问题');
