<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chart.js CDN 测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: 500;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 8px;
            font-size: 14px;
        }
        button:hover {
            background: #0056b3;
        }
        .cdn-test {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
        }
        .cdn-url {
            font-family: monospace;
            background: #f8f9fa;
            padding: 5px;
            border-radius: 3px;
            word-break: break-all;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Chart.js CDN 加载测试</h1>
        
        <div id="overallStatus" class="status warning">准备测试...</div>
        
        <button onclick="testAllCDNs()">测试所有CDN</button>
        <button onclick="clearLog()">清空日志</button>
        
        <h3>CDN测试结果</h3>
        <div id="cdnResults"></div>
        
        <h3>详细日志</h3>
        <div id="testLog" class="log">等待测试...</div>
    </div>

    <script>
        const cdnUrls = [
            'https://cdnjs.cloudflare.com/ajax/libs/Chart.js/4.4.0/chart.min.js',
            'https://unpkg.com/chart.js@4.4.0/dist/chart.min.js',
            'https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js',
            'https://cdn.jsdelivr.net/npm/chart.js/dist/chart.min.js'
        ];

        let logElement = document.getElementById('testLog');
        let statusElement = document.getElementById('overallStatus');
        let resultsElement = document.getElementById('cdnResults');

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            logElement.textContent += logEntry;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(logEntry);
        }

        function clearLog() {
            logElement.textContent = '';
            resultsElement.innerHTML = '';
        }

        function updateStatus(message, type) {
            statusElement.textContent = message;
            statusElement.className = `status ${type}`;
        }

        function testCDN(url, index) {
            return new Promise((resolve) => {
                log(`测试CDN ${index + 1}: ${url}`, 'info');
                
                const script = document.createElement('script');
                script.src = url;
                
                const timeout = setTimeout(() => {
                    log(`CDN ${index + 1} 超时`, 'error');
                    resolve({ url, success: false, error: 'timeout' });
                }, 10000);
                
                script.onload = function() {
                    clearTimeout(timeout);
                    
                    // 检查Chart.js是否真的加载了
                    if (typeof Chart !== 'undefined') {
                        log(`✓ CDN ${index + 1} 成功加载Chart.js`, 'success');
                        resolve({ url, success: true, version: Chart.version });
                    } else {
                        log(`✗ CDN ${index + 1} 脚本加载但Chart未定义`, 'error');
                        resolve({ url, success: false, error: 'Chart not defined' });
                    }
                    
                    // 清理
                    document.head.removeChild(script);
                    if (typeof Chart !== 'undefined') {
                        delete window.Chart;
                    }
                };
                
                script.onerror = function() {
                    clearTimeout(timeout);
                    log(`✗ CDN ${index + 1} 加载失败`, 'error');
                    resolve({ url, success: false, error: 'load failed' });
                    document.head.removeChild(script);
                };
                
                document.head.appendChild(script);
            });
        }

        async function testAllCDNs() {
            log('开始测试所有Chart.js CDN...', 'info');
            updateStatus('正在测试CDN...', 'warning');
            resultsElement.innerHTML = '';
            
            let successCount = 0;
            
            for (let i = 0; i < cdnUrls.length; i++) {
                const result = await testCDN(cdnUrls[i], i);
                
                const resultDiv = document.createElement('div');
                resultDiv.className = 'cdn-test';
                
                if (result.success) {
                    successCount++;
                    resultDiv.innerHTML = `
                        <div class="status success">
                            ✓ CDN ${i + 1} 成功 (版本: ${result.version})
                        </div>
                        <div class="cdn-url">${result.url}</div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="status error">
                            ✗ CDN ${i + 1} 失败 (${result.error})
                        </div>
                        <div class="cdn-url">${result.url}</div>
                    `;
                }
                
                resultsElement.appendChild(resultDiv);
                
                // 短暂延迟避免并发问题
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
            
            log(`测试完成: ${successCount}/${cdnUrls.length} CDN可用`, 'info');
            
            if (successCount > 0) {
                updateStatus(`${successCount}/${cdnUrls.length} CDN可用`, 'success');
            } else {
                updateStatus('所有CDN都不可用', 'error');
            }
        }

        // 页面加载时自动检查当前Chart.js状态
        window.addEventListener('load', function() {
            log('页面加载完成', 'info');
            
            if (typeof Chart !== 'undefined') {
                log(`当前已有Chart.js加载，版本: ${Chart.version}`, 'success');
                updateStatus('Chart.js已加载', 'success');
            } else {
                log('当前没有Chart.js加载', 'warning');
                updateStatus('Chart.js未加载', 'warning');
            }
        });

        // 错误捕获
        window.addEventListener('error', function(event) {
            log(`JavaScript错误: ${event.error.message} 在 ${event.filename}:${event.lineno}`, 'error');
        });
    </script>
</body>
</html>
